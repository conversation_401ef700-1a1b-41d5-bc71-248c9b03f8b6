{"tasks": [{"id": 1, "title": "Setup NestJS Project and Dependencies", "description": "Set up the core NestJS application structure, configure TypeScript, and install initial dependencies including testing frameworks.", "details": "Initialize a new NestJS project using the NestJS CLI (v10+ recommended). Configure TypeScript 5.7.3. Install core dependencies: `@nestjs/core`, `@nestjs/common`, `@nestjs/platform-express`. Install testing dependencies: `jest` 29.7.0, `supertest` 7.0.0, `@types/jest`, `@types/supertest`. Configure Jest for TypeScript. Set up basic project structure with modules for core functionalities.", "testStrategy": "Verify project structure, successful dependency installation, and basic Jest configuration by running a simple test case.", "priority": "high", "dependencies": [], "status": "done", "subtasks": []}, {"id": 2, "title": "Configure MongoDB and Initial Models", "description": "Configure database connections for MongoDB using Mongoose and set up initial data models.", "details": "Install Mongoose 8.13.2 (`mongoose`, `@nestjs/mongoose`). Configure the MongoDB connection string in the NestJS application (e.g., using `ConfigModule`). Define a basic Mongoose schema and model (e.g., for a 'User' or 'Setting') to test the connection. While TypeORM 0.3.22 is mentioned, prioritize Mongoose as the primary ORM for MongoDB as per the PRD's explicit mention.", "testStrategy": "Implement a simple service to connect to MongoDB and perform a basic operation (e.g., create/find a dummy record). Verify successful connection and operation via logs or a test endpoint.", "priority": "high", "dependencies": [1], "status": "done", "subtasks": []}, {"id": 3, "title": "Implement Basic User Authentication and Authorization", "description": "Implement basic user authentication and authorization using JWT.", "details": "Install authentication dependencies: `@nestjs/passport`, `passport`, `passport-jwt`, `@nestjs/jwt`, `jsonwebtoken`. Implement a `AuthModule` with `JwtStrategy`. Create endpoints for user registration and login. Generate and return JWT tokens upon successful authentication. Implement basic guards (`@nestjs/common` `AuthGuard`) to protect routes. Define basic user roles (e.g., Teacher, <PERSON><PERSON>) and implement role-based authorization using `@nestjs/common` `CanActivate` or a custom decorator.", "testStrategy": "Write integration tests using Supertest to verify user registration, login with correct/incorrect credentials, and access to protected routes with/without a valid JWT.", "priority": "high", "dependencies": [1, 2], "status": "done", "subtasks": []}, {"id": 4, "title": "Integrate Basic AI Services (OpenAI, Google AI)", "description": "Integrate OpenAI and Google Generative AI services and create a service to handle API calls.", "details": "Install AI SDKs: `openai` 4.94.0, `@google/generative-ai` 0.24.0. Create an `AiModule` and an `AiService`. Configure API keys securely (e.g., using `ConfigModule`). Implement methods in `AiService` to make basic text generation calls to both OpenAI and Google AI APIs. Implement basic error handling and logging for API calls. Consider a simple factory or strategy to switch between providers.", "testStrategy": "Write unit tests for the `AiService` methods, mocking the external API calls to ensure correct request parameters are formed and responses are handled. Write integration tests to make actual calls to verify connectivity and basic functionality (using test API keys if available).", "priority": "high", "dependencies": [1], "status": "done", "subtasks": []}, {"id": 5, "title": "Define Educational Content Data Models", "description": "Define Mongoose schemas and models for the educational content hierarchy (Subject, Topic, Question, etc.).", "details": "Define Mongoose schemas for `Subject`, `Topic`, `Question`, `Worksheet`, etc. Model the hierarchy: `Topic` belongs to `Subject`, `Subject` can have a `parentSubject`. Include fields for question types, difficulty levels, cognitive complexity, curriculum alignment metadata. Establish relationships between models (e.g., `Worksheet` contains `Question` references). Use Mongoose features like population for querying related data.", "testStrategy": "Write unit tests for each schema to ensure correct field types, required fields, and relationships are defined. Use Mongoose model methods in tests to create and query documents, verifying data integrity and relationships.", "priority": "high", "dependencies": [2], "status": "done", "subtasks": []}, {"id": 6, "title": "Develop Prompt Engineering System", "description": "Develop a system for building dynamic and context-aware prompts for AI content generation.", "details": "Create a `PromptService` that takes parameters like subject, topic, cognitive level, question type, difficulty, and curriculum context (retrieved from DB via models defined in Task 5). Implement logic to construct well-structured prompts for the AI services (OpenAI, Google AI) using prompt templates. Research and apply best practices for educational content prompt engineering (e.g., specifying output format, tone, target age group).", "testStrategy": "Write unit tests for the `PromptService` to verify that prompts are correctly generated based on various input parameters and combinations. Test edge cases and different content types.", "priority": "high", "dependencies": [4, 5], "status": "done", "subtasks": []}, {"id": 7, "title": "Implement Question Type Strategy Pattern", "description": "Implement the Strategy pattern to handle different question types.", "details": "Define a TypeScript interface (e.g., `IQuestionGeneratorStrategy`) with a method like `generate(prompt: string, options: any): Promise<Question>`. Create concrete classes implementing this interface for different question types (e.g., `MultipleChoiceStrategy`, `FillInBlankStrategy`). This promotes modularity and allows adding new question types easily.", "testStrategy": "Write unit tests to ensure the interface is correctly defined and that concrete strategy classes adhere to the interface contract. Test basic instantiation of strategy classes.", "priority": "medium", "dependencies": [1], "status": "done", "subtasks": []}, {"id": 8, "title": "Build Question Type Factories", "description": "Create factories to select and instantiate the correct question generation strategy based on input.", "details": "Create a `QuestionStrategyFactory` service that takes the desired question type as input and returns the appropriate strategy implementation (e.g., an instance of `MultipleChoiceStrategy`). This factory will use the prompt generated by the `PromptService` (Task 6) and the strategies defined in Task 7. Inject necessary dependencies (like the `AiService`) into the strategies via the factory or dependency injection.", "testStrategy": "Write unit tests for the `QuestionStrategyFactory` to verify that it returns the correct strategy instance for each supported question type input. Test handling of unsupported types.", "priority": "high", "dependencies": [6, 7], "status": "done", "subtasks": []}, {"id": 9, "title": "Implement Basic Content Validation", "description": "Implement basic validation logic for generated educational content.", "details": "Create a `ContentValidationService`. Implement basic validation rules based on PRD requirements: age-appropriateness heuristics (e.g., vocabulary complexity), basic cultural sensitivity checks (initial simple checks), format validation (e.g., ensuring MathML is present for math questions, checking structure for MCQs). This service will be used after AI generation.", "testStrategy": "Write unit tests for the `ContentValidationService` with various inputs (valid and invalid content examples) to ensure validation rules are applied correctly and the service returns the expected validation status or errors.", "priority": "medium", "dependencies": [1], "status": "done", "subtasks": []}, {"id": 10, "title": "Develop Basic Worksheet Generation Workflow", "description": "Develop the core workflow for generating a worksheet using AI-powered content generation.", "details": "Create a `WorksheetService` and an API endpoint (e.g., POST /worksheets/generate). This endpoint will receive parameters (subject, topic, difficulty, number of questions, question types, etc.). The service will orchestrate the process: use the `PromptService` (Task 6) to get prompts, use the `QuestionStrategyFactory` (Task 8) to get strategies, call the strategies to generate questions via the `AiService` (Task 4), validate generated content using `ContentValidationService` (Task 9), and save the generated `Worksheet` and `Question` data to the database (Task 5).", "testStrategy": "Write integration tests using Supertest for the worksheet generation endpoint. Mock external AI calls if necessary, but test the full internal flow: receiving parameters, calling services, saving to DB. Verify that a worksheet and associated questions are created in the database with the correct structure.", "priority": "high", "dependencies": [8, 9], "status": "done", "subtasks": []}, {"id": 11, "title": "Integrate Vector Databases (Qdrant, Pinecone)", "description": "Integrate Qdrant and Pinecone vector databases into the application.", "details": "Install client libraries for Qdrant and Pinecone (e.g., `@qdrant/qdrant-js`, `@pinecone-database/pinecone`). Create a `VectorDbModule` and a `VectorDbService`. Configure connections to both databases securely. Implement basic methods for creating collections/indexes and inserting vectors. Decide on a primary vector DB for initial implementation based on research (e.g., Qdrant for self-hosting flexibility, Pinecone for managed service ease) or implement a facade pattern to support both.", "testStrategy": "Write integration tests for the `VectorDbService` to connect to the databases (using test instances or mocks), create a dummy collection/index, and attempt to insert a dummy vector. Verify successful connection and basic operation.", "priority": "medium", "dependencies": [1, 2], "status": "done", "subtasks": []}, {"id": 12, "title": "Implement Embedding Strategies and Semantic Search", "description": "Implement embedding generation and semantic search capabilities using the integrated vector databases.", "details": "Use an embedding model (e.g., OpenAI's `text-embedding-ada-002` via the `openai` SDK from Task 4, or a dedicated embedding library/service) to generate vector embeddings for educational content (curriculum text, sample questions). Implement logic in `VectorDbService` to index these embeddings into Qdrant/Pinecone collections (Task 11). Implement a semantic search method that takes a query, generates its embedding, and queries the vector database for similar content vectors. Implement basic query expansion techniques.", "testStrategy": "Write integration tests: generate embeddings for sample texts, index them in the vector DB. Perform semantic search queries with related texts and verify that the expected similar items are returned (within reasonable similarity thresholds).", "priority": "medium", "dependencies": [4, 11], "status": "done", "subtasks": []}, {"id": 13, "title": "Set up BullMQ for Background Processing", "description": "Set up BullMQ for background job processing.", "details": "Install BullMQ 5.49.2 (`bullmq`). Install Redis client (`ioredis` recommended). Configure a Redis connection for BullMQ (can reuse the connection from Task 2 if applicable, or set up a dedicated one). Create a basic BullMQ queue (e.g., `worksheetGenerationQueue`). Implement a simple job producer to add jobs to the queue and a basic worker to process them. Integrate this with NestJS using `@nestjs/bullmq`.", "testStrategy": "Write integration tests to verify that jobs can be successfully added to the BullMQ queue and that a basic worker can pick up and process a job. Monitor Redis to confirm queue state changes.", "priority": "medium", "dependencies": [1, 2], "status": "done", "subtasks": []}, {"id": 14, "title": "Implement Real-time Collaboration Infrastructure (Socket.IO)", "description": "Implement the basic infrastructure for real-time collaboration using Socket.IO.", "details": "Install Socket.IO 4.8.1 (`socket.io`, `@nestjs/websockets`, `@nestjs/platform-socket.io`). Create a NestJS WebSocket Gateway (e.g., `CollaborationGateway`). Configure the gateway to listen for connections and basic events (e.g., `joinRoom`, `sendMessage`). Implement basic room management logic. This sets up the foundation for real-time features.", "testStrategy": "Write integration tests using a Socket.IO client library in the test suite. Connect to the WebSocket gateway, join a room, send a message, and verify that the message is received by other clients in the same room (simulated in the test).", "priority": "medium", "dependencies": [1], "status": "done", "subtasks": []}, {"id": 15, "title": "Develop Basic Question Pool Management", "description": "Develop the basic system for storing, retrieving, and managing questions in a pool.", "details": "Use the `Question` model defined in Task 5. Create a `QuestionPoolService` and API endpoints (e.g., GET /questions, POST /questions, GET /questions/:id). Implement basic CRUD operations for questions in the pool. Include fields for metadata relevant to the pool (e.g., source, quality score). Implement a basic retrieval method to fetch questions based on criteria (subject, topic, type).", "testStrategy": "Write integration tests using Supertest for the question pool API endpoints. Test creating, retrieving (single and list), updating, and deleting questions. Verify data persistence and retrieval accuracy.", "priority": "medium", "dependencies": [5], "status": "done", "subtasks": []}, {"id": 16, "title": "Implement Advanced Random Question Retrieval in QuestionPoolService", "description": "Implement the `getRandomQuestions` method in `QuestionPoolService` using a MongoDB aggregation pipeline for randomized question selection, supporting comprehensive filtering (including by `difficultyLevel`) and distribution rule enforcement.", "status": "done", "dependencies": [2, 5, 15], "priority": "medium", "details": "Define the `getRandomQuestions` method within the existing `QuestionPoolService` (from Task 15). The method should accept parameters for filtering: `subjectId`, `parentSubjectId`, `childSubjectId`, `questionType`, `gradeLevel`, `language`, `difficultyLevel`, and `count` (number of questions to retrieve). Implement the core logic using a MongoDB aggregation pipeline. Utilize the `$match` stage for applying all specified filters based on the `Question` model fields (defined in Task 5), handling optional filters (including `difficultyLevel`). Employ the `$sample` stage for true randomization of the matched questions, selecting the desired `count`. Incorporate logic to enforce distribution rules for question types and difficulty levels, referring to the 'random question pool selection feature documentation' for specifics; this may involve multiple aggregation queries or post-processing. Implement robust error handling, including input validation, handling no-match scenarios, and database errors. Consider fallback mechanisms as per documentation. The method should return a list of `Question` objects or a structured response. Leverage Mongoose models (Task 5) and MongoDB connection (Task 2).", "testStrategy": "Unit Tests: Create comprehensive unit tests for `QuestionPoolService.getRandomQuestions`. Mock MongoDB interactions to test aggregation pipeline construction and logic. Test with various filter combinations, edge cases (0 questions, more questions than available), and verify `$sample` usage. Integration Tests (against a test database): Populate a test MongoDB with diverse questions. Test filtering by `subjectId`, `parentSubjectId`, `childSubjectId`, `questionType`, `gradeLevel`, `language`, `difficultyLevel`, and combined filters. Verify correct `count` and randomization across multiple calls. Distribution Rules Testing: Set up test data and call `getRandomQuestions` to trigger distribution rules. Verify adherence to question type and difficulty level distributions. Error Handling Tests: Test with invalid filter parameters and no-match scenarios, ensuring appropriate responses. Fallback Mechanism Tests: If implemented, test scenarios triggering fallback behavior.", "subtasks": []}, {"id": 17, "title": "Enhance WorksheetGenerateConsumer with Hybrid Question Sourcing (Pool & AI)", "description": "Modify `WorksheetGenerateConsumer` to implement a hybrid question sourcing strategy, prioritizing questions from a predefined pool and using AI generation as a fallback. This includes configuration options for question source selection and maintaining existing progress tracking and WebSocket update functionalities.", "details": "1. **Modify `WorksheetGenerateConsumer`**: Update the consumer logic to integrate with `QuestionPoolService` (Task 16) for fetching questions and with `AiService` (Task 4) and `QuestionStrategyFactory` (Task 8) for AI question generation.\n2. **Implement Hybrid Strategy (Pool-First, AI Fallback)**: First, attempt to retrieve questions from the pool using `QuestionPoolService.getRandomQuestions`. If insufficient, calculate the deficit and trigger AI question generation for the remaining questions using appropriate strategies and AI services. Apply filters (subject, topic, type, difficulty, etc.) as specified.\n3. **Configuration for Question Source**: Introduce a configuration parameter in the worksheet generation request or consumer settings to specify the question source: `POOL_ONLY`, `AI_ONLY`, `HYBRID_POOL_FIRST` (default). \n4. **Question Type Mapping and Validation**: Ensure seamless mapping between requested question types and those available/generated. Utilize `QuestionStrategyFactory` (Task 8) for AI generation of specific types. Integrate `ContentValidationService` (Task 9) to validate all questions.\n5. **Maintain Existing Functionality**: Ensure existing progress tracking mechanisms and WebSocket updates (Task 14) remain functional.\n6. **Error Handling and Logging**: Implement robust error handling for pool issues, AI API failures, or validation failures. Add detailed logging for the question sourcing process.", "testStrategy": "1. **Unit Tests**: Test `WorksheetGenerateConsumer` logic for each source configuration (`POOL_ONLY`, `AI_ONLY`, `HYBRID_POOL_FIRST`). Mock dependencies (`QuestionPoolService`, AI services, `QuestionStrategyFactory`) to verify correct calls and logic for deficit calculation and AI fallback. Test type mapping and validation integration.\n2. **Integration Tests**: Test end-to-end flow with `WorksheetGenerateConsumer` in BullMQ (Task 13), interacting with `QuestionPoolService` (Task 16) and `AiService` (Task 4). Test scenarios: pool has enough questions, pool has some questions (triggering AI fallback), pool has no relevant questions.\n3. **Functional Tests**: Trigger worksheet generation jobs with different source configurations. Verify generated worksheets contain questions from expected sources, correct number/types of questions, and that content validation (Task 9) is applied. Verify progress tracking and WebSocket updates (Task 14) function correctly. Check logs for sourcing details.", "status": "done", "dependencies": [4, 8, 9, 13, 14, 16], "priority": "medium", "subtasks": [{"id": 1, "title": "Define and Integrate Question Source Configuration", "description": "Modify `WorksheetGenerateConsumer` to accept and process a new `questionSourceStrategy` configuration parameter (`POOL_ONLY`, `AI_ONLY`, `HYBRID_POOL_FIRST` with `HYBRID_POOL_FIRST` as default). This sets the foundation for conditional question sourcing logic.", "dependencies": [], "details": "Define an enum or constants for the `questionSourceStrategy` values: `PO<PERSON>_ONLY`, `AI_ONLY`, `HYBRID_POOL_FIRST`. Update the worksheet generation request DTO or consumer's input parameters to include this new field. Implement logic within the consumer to read this configuration at the start of processing, defaulting to `HYBRID_POOL_FIRST` if not specified. Store the chosen strategy for use in subsequent steps.", "status": "done", "testStrategy": "Unit test the `WorksheetGenerateConsumer` to verify it correctly parses the `questionSourceStrategy` parameter, applies the default value when the parameter is missing, and stores the chosen strategy."}, {"id": 2, "title": "Implement Question Sourcing from Question Pool", "description": "Integrate `QuestionPoolService` (Task 16) into `WorksheetGenerateConsumer`. Implement logic to fetch questions from the pool using specified filters (subject, topic, type, difficulty, etc.) when the strategy is `POOL_ONLY` or as the initial step for `HYBRID_POOL_FIRST`.", "dependencies": [1], "details": "Based on the determined `questionSourceStrategy` (from subtask 1), if `POOL_ONLY` or `HYBRID_POOL_FIRST`, call `QuestionPoolService.getRandomQuestions` with the filters derived from the worksheet generation request. Handle responses from the service, including scenarios where sufficient questions are found, insufficient questions are found, or no questions are found. Store the retrieved pool questions temporarily.", "status": "done", "testStrategy": "Mock `QuestionPoolService`. Write unit tests for `WorksheetGenerateConsumer` to cover: 1. `POOL_ONLY` strategy: fetching and using only pool questions. 2. `HYBRID_POOL_FIRST` strategy: initial attempt to fetch from pool. 3. Correct application of filters. 4. Handling of various return counts from the pool service."}, {"id": 3, "title": "Implement AI-Powered Question Generation Logic", "description": "Integrate `AiService` (Task 4) and `QuestionStrategyFactory` (Task 8) for AI-driven question generation. This logic will be triggered if the strategy is `AI_ONLY`, or as a fallback in `HYBRID_POOL_FIRST` mode if the question pool yields an insufficient number of questions.", "dependencies": [2], "details": "If `questionSourceStrategy` is `AI_ONLY`, all requested questions should be generated via AI. If `HYBRID_POOL_FIRST` and the pool fetch (subtask 2) resulted in a deficit, calculate the number of additional questions needed. Use `QuestionStrategyFactory` to select the appropriate AI generation strategy based on question type, subject, topic, and difficulty. Invoke the `AiService` to generate the required number of questions. Apply filters as specified.", "status": "done", "testStrategy": "Mock `AiService` and `QuestionStrategyFactory`. Unit test scenarios: 1. `AI_ONLY` strategy: all questions generated by AI. 2. `HYBRID_POOL_FIRST` fallback: correct deficit calculation and AI invocation for the remaining questions. 3. Verify that appropriate strategies are selected via `QuestionStrategyFactory`."}, {"id": 4, "title": "Integrate Content Validation and Type Consistency for Sourced Questions", "description": "Incorporate `ContentValidationService` (Task 9) to validate all questions, whether sourced from the pool or generated by AI. Ensure consistent handling and mapping of question types between the original request and the sourced/generated questions.", "dependencies": [3], "details": "After questions are collected (from pool, AI, or both), iterate through the combined list. For each question, invoke `ContentValidationService.validate`. Define and implement a clear strategy for handling validation failures (e.g., log the failure and discard the question, attempt to replace it if feasible, or fail the worksheet generation if too many invalid questions). Ensure that the types of the validated questions align with the worksheet specifications, potentially using `QuestionStrategyFactory` for context if AI generation was involved.", "status": "done", "testStrategy": "Mock `ContentValidationService`. Test with a list of questions containing both valid and invalid items (mocked from pool and AI). Verify that validation is called for each question and that the defined failure handling strategy is correctly implemented. Test type consistency checks."}, {"id": 5, "title": "Finalize Integration, Add Robust Error Handling & Logging, and Ensure Existing Functionality", "description": "Consolidate the complete hybrid question sourcing logic. Implement comprehensive error handling for external service calls (to `QuestionPoolService`, `AiService`) and critical validation steps. Add detailed logging throughout the sourcing process. Crucially, verify that existing functionalities like progress tracking and WebSocket updates (Task 14) remain intact and operate correctly with the new changes.", "dependencies": [4], "details": "Wrap calls to `QuestionPoolService` and `AiService` in try-catch blocks to handle potential exceptions (e.g., network issues, API errors). Implement logging for key events: chosen strategy, number of questions attempted from pool, number retrieved from pool, number attempted from AI, number generated by AI, validation results for each question, and any errors encountered. Review and test existing progress update mechanisms and WebSocket message dispatches to ensure they accurately reflect the state of the hybrid generation process. Define an overall failure strategy if an insufficient number of valid questions can be sourced after all attempts.", "status": "done", "testStrategy": "Perform end-to-end integration tests for the `WorksheetGenerateConsumer` covering all three sourcing strategies (`POOL_ONLY`, `AI_ONLY`, `HYBRID_POOL_FIRST`). Verify logs for detailed tracing of the sourcing pipeline. Test specific error scenarios (e.g., `QuestionPoolService` throws an exception, `AiService` fails, `ContentValidationService` rejects most questions). Confirm that WebSocket messages and progress updates are timely and accurate throughout the process."}]}, {"id": 18, "title": "Implement Distribution Enforcement and Balancing in Question Pool Selection", "description": "Implement algorithms to enforce specified distributions of difficulty levels (e.g., 20% Easy, 60% Medium, 20% Advanced) and balance question types during random question selection from the pool. This includes weighted selection, diversity mechanisms, and a quality validation pipeline.", "status": "done", "dependencies": [5, 9, 16], "priority": "medium", "details": "This task involves enhancing the `QuestionPoolService`, specifically the `getRandomQuestions` method (developed in Task 16), to implement sophisticated distribution enforcement and balancing algorithms.\nKey implementation aspects:\n1.  **Difficulty Level Distribution:** Modify `getRandomQuestions` to accept or retrieve configuration for target difficulty level distributions (e.g., 20% Easy, 60% Medium, 20% Advanced). Implement logic within the MongoDB aggregation pipeline or in post-processing to select questions that meet these distribution targets. The `Question` model (Task 5) must have a `difficultyLevel` field (e.g., 'Easy', 'Medium', 'Advanced').\n2.  **Question Type Balancing:** Implement mechanisms to ensure a balanced mix of question types based on configurable parameters or heuristics. The `Question` model (Task 5) must have a `questionType` field.\n3.  **Weighted Selection Mechanisms:** Introduce weighting factors in the selection process, potentially based on achieving desired distributions, question quality, or recency.\n4.  **Diversity Algorithms:** Implement strategies to prevent excessive repetition of questions. Consider adding fields like `lastSelectedTimestamp` or `selectionFrequency` to the `Question` model (ensure Task 5 accounts for this or update as part of this task). The selection algorithm should penalize or deprioritize recently or frequently selected questions.\n5.  **Quality Validation Pipeline Integration:** After initial selection, pass questions through a quality validation pipeline utilizing the `ContentValidationService` (from Task 9) for educational appropriateness checks (as per Phase 3 specifications). Questions failing validation should be handled according to defined rules.\n6.  **Configuration:** Define how distribution rules, balancing parameters, and diversity settings will be configured (e.g., global settings, per-request parameters).\n7.  **Fallback Strategies:** Define behavior if the pool cannot satisfy requested distributions or diversity criteria (e.g., best effort, logging, error handling).", "testStrategy": "1.  **Unit Tests:** Test `DistributionLogic` for difficulty level percentages, `QuestionTypeBalancingLogic`, `WeightedSelection` for correct prioritization, and `DiversityAlgorithm` for minimized repetition. Mock `ContentValidationService` to test `QualityValidationIntegration`.\n2.  **Integration Tests (within `QuestionPoolService`):** Test `getRandomQuestions` with a seeded MongoDB test database. Verify adherence to distribution/balancing parameters (including the new difficulty level distributions), diversity rule enforcement, and actual `ContentValidationService` integration.\n3.  **Scenario Tests:** Simulate generating multiple worksheets to assess overall distribution (including difficulty levels) and diversity. Test fallback strategies with insufficient or skewed pool data.\n4.  **Configuration Testing:** Verify that changes in configuration for distributions (including difficulty levels), balancing, and diversity are correctly applied by the selection algorithms.", "subtasks": [{"id": 1, "title": "Implement Core Difficulty Distribution Logic and Configuration in `getRandomQuestions`", "description": "Modify `QuestionPoolService.getRandomQuestions` to support configurable difficulty level distributions (e.g., 20% Easy, 60% Medium, 20% Advanced). This includes defining the configuration mechanism and implementing the initial selection logic to meet these targets, ensuring the `Question` model has a `difficultyLevel` field.", "dependencies": [], "details": "Define a configuration schema for difficulty distributions (e.g., `{\"Easy\": 0.2, \"Medium\": 0.6, \"Advanced\": 0.2}`). Decide if this is a global setting, per-pool setting, or a parameter to `getRandomQuestions`. Update `getRandomQuestions` method signature if necessary. Implement selection logic using MongoDB aggregation (`$facet`), multiple queries, or post-processing to select questions per difficulty level. Verify `Question` model (Task 5) includes `difficultyLevel` (e.g., 'Easy', 'Medium', 'Advanced').", "status": "done", "testStrategy": "Unit test `getRandomQuestions` with various distribution configurations and pool states. Test edge cases like insufficient questions for a specific difficulty. Mock MongoDB interactions to verify query construction and processing logic."}, {"id": 2, "title": "Integrate Question Type Balancing and Weighted Selection Framework", "description": "Extend the question selection process in `getRandomQuestions` to ensure a balanced mix of question types based on configurable parameters. Introduce a foundational weighted selection mechanism that can be used to influence choices based on various criteria. Ensure the `Question` model has a `questionType` field.", "dependencies": [1], "details": "Define configuration for question type balancing (e.g., target percentages per type, or a 'prefer diversity' flag). Modify `getRandomQuestions` to incorporate type balancing, potentially by adjusting counts for difficulty+type combinations or post-processing the difficulty-selected set. Implement a generic weighting system (e.g., adding a temporary `selectionScore` to candidate questions) that can initially help achieve difficulty/type targets. Verify `Question` model (Task 5) includes `questionType`.", "status": "done", "testStrategy": "Unit test `getRandomQuestions` with combined difficulty and type balancing configurations. Test scenarios where type balancing might conflict with difficulty targets and how it's resolved. Test the weighting mechanism's influence on selection."}, {"id": 3, "title": "Implement Diversity Algorithms using Timestamps and Frequency", "description": "Enhance the selection algorithm in `getRandomQuestions` to promote diversity and prevent excessive repetition of questions. This involves adding tracking fields to the `Question` model and using them to penalize recently or frequently selected questions, potentially leveraging the weighted selection framework.", "dependencies": [2], "details": "Add `lastSelectedTimestamp` (Date) and `selectionFrequency` (Number) fields to the `Question` model (if not already present from Task 5, consider this an update). Ensure these fields are updated after a question is selected. Modify `getRandomQuestions` to fetch these fields and use the weighting mechanism (from subtask 2) to assign lower scores/weights to questions selected recently or with high frequency. Define configurable parameters for how recency and frequency affect selection.", "status": "done", "testStrategy": "Unit test `getRandomQuestions` by simulating multiple selections. Verify that `lastSelectedTimestamp` and `selectionFrequency` are updated correctly. Test that previously selected questions are less likely to be re-selected shortly after, according to diversity settings."}, {"id": 4, "title": "Integrate Quality Validation Pipeline using `ContentValidationService`", "description": "After an initial set of questions is selected based on distribution, type, and diversity criteria, pass them through a quality validation pipeline using the `ContentValidationService` (from Task 9) for educational appropriateness checks. Implement logic for handling questions that fail validation.", "dependencies": [3], "details": "In `QuestionPoolService.getRandomQuestions`, after selection logic (difficulty, type, diversity) is complete, call `ContentValidationService.validateQuestions(selectedQuestions)`. Define and implement rules for handling questions that fail validation (e.g., discard and attempt to fetch replacements respecting original criteria, discard and return a smaller set, or log failures and proceed). Make this handling rule configurable or based on predefined policy.", "status": "done", "testStrategy": "Integration test `getRandomQuestions` with a mocked `ContentValidationService`. Test scenarios where all questions pass, some fail, and all fail. Verify the implemented handling logic for failed questions (e.g., replacement attempts, logging, correct number of questions returned)."}, {"id": 5, "title": "Implement Fallback Strategies and Finalize Comprehensive Configuration", "description": "Define and implement robust fallback strategies within `getRandomQuestions` for scenarios where the question pool cannot satisfy all requested distributions, balancing, diversity, or quality criteria. Consolidate and finalize the configuration mechanisms for all selection parameters.", "dependencies": [1, 2, 3, 4], "details": "Implement fallback logic: if exact criteria (difficulty, type, diversity) cannot be met, allow 'best effort' selection with logging. If not enough questions pass quality validation and replacements are not found, return fewer questions or throw an error based on configuration. Consolidate all configuration settings (difficulty distribution, type balancing, diversity parameters, validation failure handling, fallback behavior) into a clear structure. Ensure comprehensive logging for selection decisions, applied configurations, and triggered fallbacks.", "status": "done", "testStrategy": "Unit and integration tests for various edge cases: empty pool, pool with insufficient questions for certain criteria, configurations that are impossible to satisfy. Verify that fallback mechanisms are triggered correctly, appropriate logs are generated, and the system behaves as expected according to the defined fallback rules and configurations."}]}, {"id": 19, "title": "Optimize Random Question Pool Selection Performance", "description": "Implement a caching strategy for random question pool results using WorksheetDocumentCacheService, optimize database query performance via indexing on key fields (subject, parentSubject, grade, type, status, difficultyLevel), manage MongoDB connection pooling, and establish performance monitoring with metrics collection for the question selection process.", "status": "done", "dependencies": [18, 5, 2], "priority": "medium", "details": "This task focuses on enhancing the performance and scalability of the random question selection mechanism from the question pool.\n\n**1. Caching Strategy Implementation (using `WorksheetDocumentCacheService`):**\n   - Target the `getRandomQuestions` method in `QuestionPoolService` (from Task 16, enhanced by Task 18) for caching.\n   - Integrate with the `WorksheetDocumentCacheService` (assume available or provided). Define any necessary adaptations if question objects require special handling by the cache service.\n   - Develop a robust cache key generation strategy. Keys must uniquely identify a query based on all its parameters: `subjectId`, `parentSubjectId`, `childSubjectId`, `questionType`, `gradeLevel`, `language`, `difficultyLevel`, `count`, and any distribution rules (from Task 18).\n   - Implement logic to populate the cache with successful, non-empty results from `getRandomQuestions`.\n   - Implement cache invalidation strategies:\n     - **Time-To-Live (TTL):** Configure suitable TTLs for cached question sets based on content volatility.\n     - **Event-driven (Optional):** If feasible, explore invalidating or updating specific cache entries when underlying questions in the pool are created, updated, or deleted, particularly if they match criteria of cached queries.\n\n**2. Database Indexing Optimization (MongoDB):**\n   - Analyze query patterns from the MongoDB aggregation pipeline in `getRandomQuestions` (Task 16/18).\n   - Identify and create optimal compound indexes on the `questions` collection to support filtering, sorting, and aggregation stages. Fields for consideration include `subjectId`, `parentSubjectId`, `childSubjectId`, `questionType`, `gradeLevel`, `language`, `difficultyLevel`, and `status`.\n   - Example indexes (to be refined based on actual query analysis):\n     - `{ \"subjectId\": 1, \"gradeLevel\": 1, \"questionType\": 1, \"status\": 1 }`\n     - `{ \"parentSubjectId\": 1, \"gradeLevel\": 1, \"status\": 1 }`\n     - `{ \"difficultyLevel\": 1, \"status\": 1, \"questionType\": 1 }`\n   - Utilize MongoDB's `explain(\"executionStats\")` command extensively to verify index usage (e.g., presence of `IXSCAN` stages, minimized `docsExamined`) and effectiveness for various query combinations.\n\n**3. Connection Pooling Management (MongoDB Driver):**\n   - Review and fine-tune MongoDB driver connection pool settings within the NestJS application's database configuration.\n   - Adjust parameters like `maxPoolSize`, `minPoolSize`, `maxIdleTimeMS`, and `waitQueueTimeoutMS` to align with expected application load, concurrency, and performance requirements.\n   - The goal is to ensure efficient connection reuse, minimize connection latency, and prevent connection exhaustion under peak loads.\n\n**4. Performance Monitoring and Metrics Collection:**\n   - Integrate a metrics collection library (e.g., `prom-client` for Prometheus) or leverage an APM tool compatible with NestJS.\n   - Instrument the `QuestionPoolService.getRandomQuestions` method to capture and expose key performance indicators (KPIs):\n     - Overall execution time (average, p95, p99).\n     - Cache hit rate and miss rate for `WorksheetDocumentCacheService` related to question pool queries.\n     - Database query execution time (if separable).\n     - Number of questions requested versus number of questions returned.\n     - Error rates specific to the question selection operation.\n   - Expose these metrics (e.g., via a `/metrics` endpoint for Prometheus scraping) or ensure they are pushed to a centralized monitoring dashboard.\n   - Establish baseline performance targets and consider configuring alerts for significant deviations (e.g., sustained high latency, critically low cache hit rate).", "testStrategy": "**1. Caching Verification:**\n   - **Unit Tests:** Validate the cache key generation logic for correctness and uniqueness under various input parameter combinations.\n   - **Integration Tests:**\n     - Invoke `getRandomQuestions` multiple times with identical parameters. Assert that subsequent calls are significantly faster and (by mocking or inspecting the cache service) confirm that results are served from the cache.\n     - Verify that altering any query parameter results in a cache miss and triggers a fresh database query.\n     - Test cache invalidation: Modify a question in the database that should affect a cached result set. Re-query and assert that fresh data is returned. Verify TTL-based expiration.\n\n**2. Database Indexing Verification:**\n   - **Baseline Measurement:** Before applying new indexes, execute representative `getRandomQuestions` queries and record their performance using `explain(\"executionStats\")` (note `docsExamined`, execution time, stages used).\n   - **Post-Indexing Validation:** After creating indexes, re-run the same queries. Use `explain(\"executionStats\")` to confirm that the new indexes are being utilized effectively (e.g., `IXSCAN` is used, `docsExamined` is reduced, query plans are more efficient).\n   - **Load Testing:** Conduct targeted load tests on the `getRandomQuestions` endpoint before and after indexing to quantify the improvement in query response times and throughput under concurrent load.\n\n**3. Connection Pooling Verification:**\n   - **Monitoring:** During load tests, monitor MongoDB server connection statistics (`db.serverStatus().connections`) to observe the number of active, idle, and available connections. Ensure these numbers stay within the configured pool limits and that the pool behaves as expected (e.g., connections are reused).\n   - **Stress Test:** Simulate high concurrency scenarios targeting `getRandomQuestions` to verify that the application manages database connections gracefully without connection timeout errors or pool exhaustion issues.\n\n**4. Performance Monitoring Verification:**\n   - **Metrics Validation:** Manually trigger various scenarios for `getRandomQuestions` (e.g., cache hits, cache misses, successful queries, queries resulting in errors). Verify that all configured metrics (latency, cache hit/miss ratio, error counts, etc.) are accurately generated and reported to the monitoring system or endpoint.\n   - **Dashboard/Alerting Check (if applicable):** Confirm that metrics are correctly displayed on dashboards and that alerts trigger based on predefined thresholds when simulated conditions meet alert criteria.\n\n**5. Overall System Performance:**\n   - **End-to-End Load Testing:** Conduct comprehensive load tests simulating realistic user traffic patterns that heavily utilize the `getRandomQuestions` functionality.\n   - **Comparative Analysis:** Measure and compare key performance metrics (overall throughput, response time percentiles, error rates) before and after all optimizations (caching, indexing, connection pooling) are implemented to demonstrate the cumulative positive impact.", "subtasks": [{"id": 1, "title": "Analyze Query Patterns and Design Optimal Database Indexes for Question Pool", "description": "Analyze the MongoDB aggregation pipeline in `getRandomQuestions` to understand query patterns. Identify key fields (subjectId, parentSubjectId, childSubjectId, questionType, gradeLevel, language, difficultyLevel, status) for indexing and design optimal compound indexes to support filtering, sorting, and aggregation stages.", "dependencies": [], "details": "Review the existing `getRandomQuestions` method (from Task 16, enhanced by Task 18). Document common query combinations and their impact on performance. Propose a set of compound indexes based on this analysis, considering fields like `subjectId`, `parentSubjectId`, `childSubjectId`, `questionType`, `gradeLevel`, `language`, `difficultyLevel`, and `status`. The output should be a documented list of index definitions and the rationale behind them.", "status": "done", "testStrategy": "Review the proposed index designs against common and edge-case query scenarios. Theoretical validation of index coverage for different filter combinations. Peer review of the analysis and proposed indexes."}, {"id": 2, "title": "Implement and Verify Designed Database Indexes on `questions` Collection", "description": "Create the designed compound indexes on the `questions` collection in MongoDB. Use MongoDB's `explain('executionStats')` command extensively to verify that the new indexes are being used effectively (e.g., `IXSCAN` stages) and are improving query performance by minimizing `docsExamined`.", "dependencies": [1], "details": "Apply the index definitions from Subtask 1 to the MongoDB `questions` collection using appropriate migration scripts or manual application in development/staging environments. For various representative query combinations used by `getRandomQuestions`, run `explain('executionStats')` before and after index creation to measure impact. Document findings, ensuring indexes cover critical query paths and reduce scan operations without negatively impacting write performance significantly.", "status": "done", "testStrategy": "Execute `explain('executionStats')` on representative queries in a staging environment. Confirm `IXSCAN` stage is used for indexed fields. Compare `docsExamined`, `keysExamined`, and `executionTimeMillis` before and after indexing. Ensure no significant negative performance impact on other critical read/write operations."}, {"id": 3, "title": "Implement Core Caching for `getRandomQuestions` using `WorksheetDocumentCacheService`", "description": "Integrate `WorksheetDocumentCacheService` into `QuestionPoolService.getRandomQuestions`. Develop a robust cache key generation strategy based on all query parameters. Implement logic to populate the cache with successful, non-empty results from `getRandomQuestions`.", "dependencies": [2], "details": "Modify the `getRandomQuestions` method in `QuestionPoolService` to first attempt to retrieve results from `WorksheetDocumentCacheService`. If a cache miss occurs, proceed to fetch data from the database (which should now be using optimized indexes from Subtask 2) and then populate the cache with the retrieved results. The cache key must be a unique string generated from all relevant parameters: `subjectId`, `parentSubjectId`, `childSubjectId`, `questionType`, `gradeLevel`, `language`, `difficultyLevel`, `count`, and any distribution rules. Ensure question objects are serializable and compatible with `WorksheetDocumentCacheService` or implement necessary adaptations.", "status": "done", "testStrategy": "Unit tests for the cache key generation logic ensuring uniqueness and correctness. Integration tests to verify cache hit and miss scenarios: call `getRandomQuestions` multiple times with identical parameters and check if subsequent calls are faster and served from cache (verified via logs or mock behavior of the cache service). Verify that cache is populated with the correct data structure."}, {"id": 4, "title": "Implement Cache Invalidation Strategies and Fine-tune Cache Configuration", "description": "Implement Time-To-Live (TTL) based cache invalidation for cached question sets. Optionally, explore and implement event-driven invalidation if underlying questions are modified. Fine-tune cache configurations based on content volatility and system resources.", "dependencies": [3], "details": "Configure appropriate TTLs for cached question sets within `WorksheetDocumentCacheService`, balancing data freshness with performance gains. If feasible and impactful, investigate mechanisms to invalidate or update specific cache entries when underlying questions in the pool are created, updated, or deleted, particularly if they match criteria of cached queries (e.g., by listening to domain events or using a pub/sub mechanism). Review and adjust cache size limits, eviction policies, or other relevant parameters of `WorksheetDocumentCacheService`.", "status": "done", "testStrategy": "Test TTL invalidation: verify cached items expire and are removed/refreshed after the configured TTL. If event-driven invalidation is implemented, write tests to simulate question data changes and verify that corresponding cache entries are correctly invalidated or updated. Monitor cache metrics (size, evictions) under simulated load to ensure stability."}, {"id": 5, "title": "Implement Performance Monitoring, Metrics Collection, and Review MongoDB Connection Pooling", "description": "Integrate a metrics collection library (e.g., `prom-client`) to instrument `getRandomQuestions`. Capture Key Performance Indicators (KPIs) like overall execution time, cache hit/miss rates, and database query execution time. Expose these metrics. Review and fine-tune MongoDB driver connection pool settings.", "dependencies": [2, 4], "details": "In `QuestionPoolService.getRandomQuestions`, add instrumentation using `prom-client` or a similar library to record: overall execution time (average, p95, p99), cache hit rate and miss rate for `WorksheetDocumentCacheService` interactions, and database query execution time (if separable from overall logic). Expose these metrics via a `/metrics` endpoint for Prometheus scraping or ensure they are pushed to an APM. Review MongoDB driver connection pool settings (`maxPoolSize`, `minPoolSize`, `maxIdleTimeMS`, `waitQueueTimeoutMS`) in the NestJS application's database configuration. Adjust these parameters based on expected application load, concurrency, and performance requirements to ensure efficient connection reuse and prevent exhaustion.", "status": "done", "testStrategy": "Verify that the `/metrics` endpoint (or APM dashboard) correctly exposes the defined KPIs. Manually trigger `getRandomQuestions` with cache hits and misses to observe metric changes. Conduct load testing to observe metrics under stress and to validate connection pool settings (monitor for connection timeouts, pool exhaustion, or excessive connection creation). Establish baseline performance targets and consider configuring alerts for significant deviations."}]}, {"id": 20, "title": "Implement Configuration Management for Random Question Pool Selection", "description": "Implement a configuration management system for the random question pool selection feature. This includes managing environment variables, feature flags for different selection strategies, and defining a `WorksheetGenerationOptions` interface with validation and default values.", "details": "1. **Setup Configuration Module**:\n    *   Utilize NestJS's `@nestjs/config` module.\n    *   Create a typed configuration file (e.g., `question-pool.config.ts`) using `registerAs` to define and export the configuration schema. Load this using `ConfigModule.forRoot({ load: [questionPoolConfig] })`.\n    *   Ensure this configuration is globally available or imported into relevant modules.\n2. **Environment Variables & Typed Configuration**:\n    *   Define and manage the following settings, loadable from environment variables, with clear defaults in `question-pool.config.ts`:\n        *   `QUESTION_POOL_ENABLED`: boolean (default: `true`). Controls overall usage of the question pool.\n        *   `DEFAULT_SELECTION_STRATEGY`: string enum (`'pool-only'`, `'ai-only'`, `'hybrid'`, `'mixed'`; default: `'hybrid'`). Specifies the default strategy for question sourcing.\n        *   `MIN_POOL_QUESTIONS_THRESHOLD`: number (default: `10`). Minimum number of questions required in the pool for it to be considered a primary source under certain strategies.\n3. **Feature Flags for Selection Strategies (within Config)**:\n    *   Extend the typed configuration to manage feature flags for selection strategies:\n        *   `allow_pool_only_strategy`: boolean (default: `true`)\n        *   `allow_ai_only_strategy`: boolean (default: `true`)\n        *   `allow_hybrid_strategy`: boolean (default: `true`)\n        *   `allow_mixed_strategy`: boolean (default: `true`)\n    *   These flags will determine which strategies are permissible for selection when processing `WorksheetGenerationOptions`.\n4. **`WorksheetGenerationOptions` DTO Definition**:\n    *   Define a Data Transfer Object (DTO) class, `WorksheetGenerationOptionsDto`, for options related to worksheet generation, particularly those affecting question sourcing. This DTO will be used for input validation in controllers or message handlers.\n    *   Properties to include (all optional, with defaults applied by consuming services based on global config):\n        *   `selectionStrategy?: string` (e.g., `'pool-only'`, `'ai-only'`, `'hybrid'`, `'mixed'`)\n        *   `useQuestionPoolOverride?: boolean`\n        *   `minPoolQuestionsRequired?: number`\n    *   **Validation**: Use `class-validator` decorators within the DTO for robust validation:\n        *   `@IsOptional() @IsEnum(['pool-only', 'ai-only', 'hybrid', 'mixed']) selectionStrategy?: string;` (further validation against active feature flags will be done in service layer).\n        *   `@IsOptional() @IsBoolean() useQuestionPoolOverride?: boolean;`\n        *   `@IsOptional() @IsInt() @Min(0) minPoolQuestionsRequired?: number;`\n5. **Configuration Service/Injection**:\n    *   Ensure the typed configuration (e.g., `QuestionPoolConfigType` from `question-pool.config.ts`) is injectable into services like `QuestionPoolService`, `WorksheetGenerateConsumer`, etc., using `@Inject(questionPoolConfig.KEY)`.\n    *   Consuming services will use these global configurations as defaults and then apply overrides from `WorksheetGenerationOptionsDto` if provided and valid.", "testStrategy": "1. **Unit Tests for Configuration Loading (`question-pool.config.ts`)**:\n    *   Verify correct loading of environment variables (e.g., using `process.env` mocks).\n    *   Test that default values are correctly applied when specific environment variables are not set.\n    *   Test that environment variables correctly override the default values for `QUESTION_POOL_ENABLED`, `DEFAULT_SELECTION_STRATEGY`, and `MIN_POOL_QUESTIONS_THRESHOLD`.\n2. **Unit Tests for Feature Flag Logic (within Config or Consuming Service)**:\n    *   Verify that feature flags for selection strategies (`allow_pool_only_strategy`, etc.) are correctly read from the configuration.\n    *   Test logic that checks if a requested strategy in `WorksheetGenerationOptionsDto` is permitted based on these flags.\n3. **Unit Tests for `WorksheetGenerationOptionsDto` Validation**:\n    *   Use `class-validator`'s `validate` function to test the DTO.\n    *   Test with valid inputs for all properties.\n    *   Test with invalid inputs: incorrect enum for `selectionStrategy`, non-boolean for `useQuestionPoolOverride`, non-integer or negative for `minPoolQuestionsRequired`.\n    *   Verify that validation errors are correctly reported.\n4. **Integration Tests for Config Injection and Usage**:\n    *   Create a test NestJS module that registers the `ConfigModule` with `question-pool.config.ts`.\n    *   Inject the typed configuration into a simple test service.\n    *   Assert that the test service receives the correct configuration values based on mock environment settings.\n5. **Service-Level Tests (Focus on Config Consumption)**:\n    *   For services like `WorksheetGenerateConsumer` or `QuestionPoolService` (or simplified versions for testing this aspect):\n        *   Verify they correctly access and utilize `QUESTION_POOL_ENABLED`.\n        *   Verify they use `DEFAULT_SELECTION_STRATEGY` when `WorksheetGenerationOptionsDto.selectionStrategy` is undefined.\n        *   Verify they prioritize `WorksheetGenerationOptionsDto.selectionStrategy` if provided and allowed by feature flags.\n        *   Test the behavior when `WorksheetGenerationOptionsDto.selectionStrategy` is provided but not allowed by feature flags (e.g., error, fallback to default).", "status": "done", "dependencies": [1, 15], "priority": "medium", "subtasks": [{"id": 1, "title": "Setup NestJS Configuration Module and Initial `question-pool.config.ts`", "description": "Initialize and configure the `@nestjs/config` module. Create the `question-pool.config.ts` file using `registerAs` to define the basic structure for question pool related configurations, and ensure it's loaded globally.", "dependencies": [], "details": "1. Install `@nestjs/config` package if not already present.\n2. Create a new configuration file, e.g., `src/config/question-pool.config.ts`.\n3. Inside this file, use `registerAs` from `@nestjs/config` to define a configuration namespace, e.g., `questionPoolConfig = registerAs('questionPool', () => ({}))`. This will be populated in subsequent tasks.\n4. In the main application module (e.g., `app.module.ts`) or a relevant feature module, import `ConfigModule` from `@nestjs/config` and `questionPoolConfig`.\n5. Configure `ConfigModule` to load the custom configuration: `ConfigModule.forRoot({ load: [questionPoolConfig], isGlobal: true })`. Setting `isGlobal: true` makes the configuration available throughout the application without needing to import `ConfigModule` in every module.", "status": "done", "testStrategy": "1. Verify the application starts without errors after these changes.\n2. Attempt to inject a basic, empty configuration object related to `questionPoolConfig.KEY` in a test service to ensure the module is correctly set up and the configuration namespace is registered."}, {"id": 2, "title": "Define Core Settings (Env Vars & Defaults) in `question-pool.config.ts`", "description": "Define and integrate `QUESTION_POOL_ENABLED`, `DEFAULT_SELECTION_STRATEGY`, and `MIN_POOL_QUESTIONS_THRESHOLD` into `question-pool.config.ts`. Ensure these are loadable from environment variables with specified defaults and properly typed.", "dependencies": [1], "details": "1. Modify `src/config/question-pool.config.ts` to include the following properties within the `registerAs` callback:\n   - `QUESTION_POOL_ENABLED`: Load from `process.env.QUESTION_POOL_ENABLED`. Parse as boolean. Default to `true`.\n   - `DEFAULT_SELECTION_STRATEGY`: Load from `process.env.DEFAULT_SELECTION_STRATEGY`. String enum (`'pool-only'`, `'ai-only'`, `'hybrid'`, `'mixed'`). Default to `'hybrid'`.\n   - `MIN_POOL_QUESTIONS_THRESHOLD`: Load from `process.env.MIN_POOL_QUESTIONS_THRESHOLD`. Parse as integer. Default to `10`.\n2. Define a TypeScript interface or type (e.g., `QuestionPoolConfig`) representing the structure of this configuration object and use it with `registerAs` for type safety: `registerAs<QuestionPoolConfig>('questionPool', () => ({...}))`.\n3. Implement parsing logic for environment variables (e.g., `parseInt` for numbers, string to boolean conversion for booleans) and ensure defaults are applied if environment variables are not set.", "status": "done", "testStrategy": "1. Unit test the configuration loading logic for these settings:\n   - Verify default values are correctly applied when corresponding environment variables are not set.\n   - Verify that values from environment variables correctly override the defaults.\n   - Verify that types are correctly parsed (e.g., string 'false' becomes boolean false, string '20' becomes number 20).\n2. Test injection of the typed configuration into a dummy service and check if the values are accessible and correct."}, {"id": 3, "title": "Add Selection Strategy Feature Flags to `question-pool.config.ts`", "description": "Extend `question-pool.config.ts` to include boolean feature flags: `allow_pool_only_strategy`, `allow_ai_only_strategy`, `allow_hybrid_strategy`, `allow_mixed_strategy`. These should also be loadable from environment variables with defaults.", "dependencies": [2], "details": "1. Further modify `src/config/question-pool.config.ts` by adding the following properties to the object returned by the `registerAs` callback:\n   - `allow_pool_only_strategy`: Load from `process.env.ALLOW_POOL_ONLY_STRATEGY`. Parse as boolean. Default to `true`.\n   - `allow_ai_only_strategy`: Load from `process.env.ALLOW_AI_ONLY_STRATEGY`. Parse as boolean. Default to `true`.\n   - `allow_hybrid_strategy`: Load from `process.env.ALLOW_HYBRID_STRATEGY`. Parse as boolean. Default to `true`.\n   - `allow_mixed_strategy`: Load from `process.env.ALLOW_MIXED_STRATEGY`. Parse as boolean. Default to `true`.\n2. Update the `QuestionPoolConfig` TypeScript interface/type defined in Subtask 2 to include these new feature flag properties.\n3. Ensure robust parsing for these boolean flags from environment variables, similar to `QUESTION_POOL_ENABLED`.", "status": "done", "testStrategy": "1. Unit test the configuration loading logic for these new feature flags:\n   - Verify default values are correctly applied.\n   - Verify that environment variables correctly override the defaults for each flag.\n2. Test injection of the updated typed configuration into a dummy service and check if all feature flag values are accessible and correct."}, {"id": 4, "title": "Define `WorksheetGenerationOptionsDto` with `class-validator` Rules", "description": "Create the `WorksheetGenerationOptionsDto` class with optional properties: `selectionStrategy`, `useQuestionPoolOverride`, and `minPoolQuestionsRequired`. Implement robust input validation for these properties using `class-validator` decorators.", "dependencies": [], "details": "1. Install `class-validator` and `class-transformer` packages if not already present.\n2. Create a new DTO file, e.g., `src/dtos/worksheet-generation-options.dto.ts`.\n3. Define the `WorksheetGenerationOptionsDto` class with the following optional properties and `class-validator` decorators:\n   - `selectionStrategy?: string;`\n     - Decorators: `@IsOptional()`, `@IsEnum(['pool-only', 'ai-only', 'hybrid', 'mixed'], { message: 'Invalid selection strategy. Must be one of: pool-only, ai-only, hybrid, mixed' })`\n   - `useQuestionPoolOverride?: boolean;`\n     - Decorators: `@IsOptional()`, `@IsBoolean()`\n   - `minPoolQuestionsRequired?: number;`\n     - Decorators: `@IsOptional()`, `@IsInt()`, `@Min(0)`\n4. This DTO will be used in controllers or message handlers to validate incoming requests or messages related to worksheet generation options.", "status": "done", "testStrategy": "1. Write unit tests for the `WorksheetGenerationOptionsDto` using the `validate` function from `class-validator`:\n   - Test with valid inputs for each property (and combinations).\n   - Test with invalid inputs for each property (e.g., wrong type for `selectionStrategy`, non-boolean for `useQuestionPoolOverride`, negative or non-integer for `minPoolQuestionsRequired`).\n   - Test that all properties are indeed optional (i.e., an empty object or an object with a subset of properties is valid if the provided properties themselves are valid)."}, {"id": 5, "title": "Implement Configuration Injection and Service-Level Override Logic", "description": "Ensure the typed `QuestionPoolConfigType` (from `question-pool.config.ts`) is injectable into relevant services (e.g., `QuestionPoolService`, `WorksheetGenerateConsumer`). Implement logic within these services to use global configurations as defaults, apply valid overrides from `WorksheetGenerationOptionsDto`, and perform service-level validation such as checking `selectionStrategy` against active feature flags.", "dependencies": [2, 3, 4], "details": "1. In services that handle worksheet generation or question selection (e.g., `WorksheetService`, `QuestionPoolService`):\n   - Inject the typed configuration using `@Inject(questionPoolConfig.KEY) private readonly config: QuestionPoolConfigType` (assuming `QuestionPoolConfigType` is the exported type of your `questionPoolConfig`).\n2. Implement or refactor methods that determine the final worksheet generation settings. These methods will typically accept an optional `WorksheetGenerationOptionsDto`.\n3. The logic should be as follows:\n   a. Start with default values derived from the injected `this.config` (e.g., `effectiveStrategy = this.config.DEFAULT_SELECTION_STRATEGY`, `effectiveMinPoolQuestions = this.config.MIN_POOL_QUESTIONS_THRESHOLD`).\n   b. If a `WorksheetGenerationOptionsDto` (let's call it `optionsDto`) is provided:\n      i. If `optionsDto.selectionStrategy` is present, check if this strategy is allowed by the corresponding feature flag in `this.config` (e.g., if `optionsDto.selectionStrategy === 'pool-only'`, check `this.config.allow_pool_only_strategy`). If allowed, update `effectiveStrategy`. If not allowed, either fall back to `this.config.DEFAULT_SELECTION_STRATEGY` or handle as an error, based on requirements.\n      ii. If `optionsDto.useQuestionPoolOverride` is present, its value should influence whether the question pool is used (this might interact with `QUESTION_POOL_ENABLED` and the chosen strategy).\n      iii. If `optionsDto.minPoolQuestionsRequired` is present and valid (non-negative integer), update `effectiveMinPoolQuestions`.\n4. Ensure that the overall `QUESTION_POOL_ENABLED` config flag is respected as a primary control.", "status": "done", "testStrategy": "1. Unit test the service logic responsible for resolving the final generation settings:\n   - Scenario: No `WorksheetGenerationOptionsDto` provided – verify that global defaults from the injected configuration are used.\n   - Scenario: `WorksheetGenerationOptionsDto` provides valid overrides for `useQuestionPoolOverride` and `minPoolQuestionsRequired` – verify these are applied correctly.\n   - Scenario: `WorksheetGenerationOptionsDto` provides a `selectionStrategy` that is allowed by its feature flag – verify the DTO's strategy is used.\n   - Scenario: `WorksheetGenerationOptionsDto` provides a `selectionStrategy` that is *disallowed* by its feature flag – verify the system falls back to the default strategy or handles the error appropriately.\n   - Scenario: `QUESTION_POOL_ENABLED` is false – verify that pool-dependent strategies are handled correctly (e.g., might switch to 'ai-only' or error).\n2. Verify successful injection of `QuestionPoolConfigType` into the target services during application startup or in integration tests."}]}, {"id": 21, "title": "Implement Comprehensive Error Handling for Random Question Pool Selection", "description": "Implement robust error handling and recovery mechanisms for the random question pool selection process, covering database connectivity, insufficient content, AI service failures (including interactions with OpenAI SDK via OpenRouter API and Google AI), and WebSocket communication.", "status": "done", "dependencies": [4, 14, 16, 17, 19, 20], "priority": "medium", "details": "1. **MongoDB Connection Retry Logic:** Modify `QuestionPoolService` or the underlying data access layer to implement retry logic for MongoDB operations. Configure for 3 retry attempts with exponential backoff upon connection failures or transient errors during question retrieval. Log each attempt and the final status.\n2. **Insufficient Questions Graceful Degradation & AI Fallback:** Enhance the question sourcing logic (likely in `WorksheetGenerateConsumer` or an orchestrator service) to handle scenarios where the question pool (Task 16) returns fewer questions than requested. If a deficit occurs, trigger AI question generation (using services from Task 4, potentially orchestrated by logic from Task 17) to fulfill the remaining count. This fallback should be configurable (Task 20).\n3. **AI Service Failure Chain:** Implement a resilient AI interaction layer (e.g., a new `AiOrchestrationService`). This service will attempt to generate questions sequentially: first using OpenAI SDK via OpenRouter API, then upon failure, from Google AI. If both AI services fail, it will attempt to retrieve questions from a 'Cached Content' source as a final fallback. Log the outcome of each attempt and the service ultimately used.\n4. **WebSocket Error Communication:** Integrate with the WebSocket gateway (Task 14) to communicate errors effectively to the client. Define specific error event types (e.g., `worksheet:generation:error`, `database:unavailable`, `ai:service:failed`, `insufficient_questions:no_fallback`) and structured error payloads (error code, message, details). Ensure errors from all parts of the question selection and generation pipeline are caught and propagated correctly.\n5. **Comprehensive Logging:** Implement detailed logging using NestJS `LoggerService` for all error events, retry attempts, fallback invocations, and AI service interactions to facilitate debugging and monitoring.", "testStrategy": "1. **MongoDB Connection Failure:** Simulate MongoDB unavailability (e.g., stop service, invalid connection string). Verify that the system attempts reconnection 3 times with backoff (check logs) and eventually communicates a DB error via WebSocket if unsuccessful.\n2. **Insufficient Questions & AI Fallback:** Configure the question pool with fewer questions than a test request. Verify the system identifies the deficit, logs it, and successfully triggers the AI fallback to generate the missing questions. Mock AI services to confirm they are called with correct parameters. The final question set should meet the requested count.\n3. **AI Service Failure Chain:** \n    a. Simulate OpenAI via OpenRouter API failure (e.g., mock endpoint to return error, invalid API key). Verify the system logs the failure and automatically attempts to use Google AI. \n    b. Simulate both OpenAI via OpenRouter API and Google AI failures. Verify the system attempts to use 'Cached Content'. \n    c. Simulate failure of all three sources. Verify a specific error is logged and communicated via WebSocket.\n4. **WebSocket Error Communication:** For each error scenario (DB down, all AI services down, insufficient questions with no successful fallback), use a WebSocket client to verify that the correct, structured error messages/events are received as defined in the error handling protocol.\n5. **System Stability:** Induce various combinations of failures to ensure no unhandled exceptions crash the application. Verify that logs provide clear and actionable information for each error scenario.", "subtasks": [{"id": 1, "title": "Implement MongoDB Connection Retry Logic in QuestionPoolService", "description": "Modify `QuestionPoolService` or the underlying data access layer to implement retry logic for MongoDB operations. This will enhance resilience against transient database connectivity issues when retrieving questions.", "dependencies": [], "details": "Configure the retry mechanism for 3 retry attempts with exponential backoff (e.g., initial delay 1s, then 2s, then 4s) upon connection failures or transient errors (e.g., network timeouts, temporary unavailability) during question retrieval operations. Log each retry attempt, including the attempt number and delay, and the final status (success or failure after all retries) using NestJS `LoggerService`. Consider using a library like `async-retry` or implementing a custom retry decorator within the service or data access layer.\n<info added on 2025-06-10T09:56:19.368Z>\nEnsure all OpenAI service interactions utilize the OpenAI SDK via the OpenRouter API, avoiding direct calls to OpenAI.\n</info added on 2025-06-10T09:56:19.368Z>", "status": "done", "testStrategy": "Unit test the retry logic by mocking MongoDB connection/query methods to throw transient errors for a specific number of calls. Verify that the correct number of retries are made with appropriate backoff. Integration test by temporarily making the MongoDB instance unavailable or simulating network issues and observing the retry behavior and logs."}, {"id": 2, "title": "Implement Graceful Degradation for Insufficient Questions with AI Fallback", "description": "Enhance the question sourcing logic, likely within `WorksheetGenerateConsumer` or an orchestrator service, to gracefully handle scenarios where the primary question pool (Task 16, accessed via `QuestionPoolService`) returns fewer questions than requested by the user or system. If a deficit occurs, trigger AI question generation to fulfill the remaining count.", "dependencies": [1], "details": "After attempting to retrieve questions from MongoDB (with retries from subtask 1), check if the number of questions obtained is less than the required number. If there's a deficit, calculate the number of missing questions. Invoke an AI question generation service (which will be the `AiOrchestrationService` from subtask 3) to generate the exact number of missing questions. This AI fallback mechanism should be configurable (as per Task 20), allowing it to be enabled or disabled. Log the detected deficit, the number of questions requested versus found, and the activation of the AI fallback using NestJS `LoggerService`.\n<info added on 2025-06-10T09:54:34.772Z>\nThe AI services mentioned in this subtask (the AI question generation service and the AI fallback mechanism) utilize the OpenAI SDK via the OpenRouter API, not direct OpenAI calls.\n</info added on 2025-06-10T09:54:34.772Z>", "status": "done", "testStrategy": "Unit test the deficit detection logic by mocking `QuestionPoolService` to return varying numbers of questions (e.g., zero, fewer than requested, exactly as requested). Verify that the AI generation service is invoked correctly with the calculated deficit when applicable. Test with the AI fallback configuration enabled and disabled."}, {"id": 3, "title": "Implement Resilient AI Service Failure Chain in AiOrchestrationService", "description": "Develop or enhance an `AiOrchestrationService` to manage sequential attempts for question generation across multiple AI providers (OpenAI SDK via OpenRouter API, then Google AI) and a final 'Cached Content' source as a fallback. This service will be invoked when the AI fallback is triggered (from subtask 2).", "dependencies": [2], "details": "The `AiOrchestrationService` will implement a chain of responsibility or sequential call pattern: 1. Attempt to generate questions using the primary AI provider (e.g., OpenAI SDK via OpenRouter API, using services from Task 4). 2. If the primary AI provider fails (e.g., API error, timeout, malformed response), attempt generation using a secondary AI provider (e.g., Google AI). 3. If all configured AI providers fail, attempt to retrieve suitable questions from a 'Cached Content' source (e.g., a pre-populated, curated list of questions in a database or file store). Log the outcome of each attempt in the chain (provider name, success/failure, error details if any) and which service (OpenAI via OpenRouter, Google AI, Cached Content, or none) ultimately fulfilled the request or if all failed. This service should be designed to be extensible for future AI providers.\n<info added on 2025-06-10T09:52:13.882Z>\nThe `AiOrchestrationService` will implement a chain of responsibility or sequential call pattern: 1. Attempt to generate questions using the primary AI provider (e.g., OpenAI SDK via OpenRouter API, using services from Task 4). 2. If the primary AI provider fails (e.g., API error, timeout, malformed response), attempt generation using a secondary AI provider (e.g., Google AI). 3. If all configured AI providers fail, attempt to retrieve suitable questions from a 'Cached Content' source (e.g., a pre-populated, curated list of questions in a database or file store). Log the outcome of each attempt in the chain (provider name, success/failure, error details if any) and which service (OpenAI SDK via OpenRouter API, Google AI, Cached Content, or none) ultimately fulfilled the request or if all failed. This service should be designed to be extensible for future AI providers.\n</info added on 2025-06-10T09:52:13.882Z>", "status": "done", "testStrategy": "Unit test `AiOrchestrationService` by mocking responses (success, failure, specific error types) from OpenAI via OpenRouter API, Google AI, and the Cached Content source. Test various scenarios: primary succeeds; primary fails, secondary succeeds; primary and secondary fail, cache succeeds; all sources fail. Verify correct logging for each step."}, {"id": 4, "title": "Integrate WebSocket Error Communication for Question Generation Failures", "description": "Integrate with the WebSocket gateway (from Task 14) to communicate errors encountered during the random question pool selection and AI generation pipeline effectively to the connected client. This ensures the client is informed of issues in real-time.", "dependencies": [1, 2, 3], "details": "Define specific error event types for WebSocket communication, such as `worksheet:generation:error`. Structure error payloads consistently, including: `errorCode` (e.g., `DATABASE_UNAVAILABLE`, `AI_SERVICE_FAILED`, `INSUFFICIENT_QUESTIONS_NO_FALLBACK`, `MAX_RETRIES_REACHED`), `message` (a user-friendly explanation), and optionally `details` (contextual information, non-sensitive). Ensure that errors originating from `QuestionPoolService` (subtask 1), insufficient question handling in `WorksheetGenerateConsumer` (subtask 2), and `AiOrchestrationService` (subtask 3) are caught and mapped to these structured WebSocket error events. For instance, if all fallbacks in `AiOrchestrationService` fail, a specific `INSUFFICIENT_QUESTIONS_NO_FALLBACK` error should be sent.\n<info added on 2025-06-10T09:55:22.316Z>\nClarification on AI service usage: All interactions with OpenAI models are performed using the OpenAI SDK through the OpenRouter API. This applies when interpreting `AI_SERVICE_FAILED` errors and understanding the AI-related functionalities of `AiOrchestrationService`.\n</info added on 2025-06-10T09:55:22.316Z>", "status": "done", "testStrategy": "Manually trigger various error conditions (e.g., disconnect database, configure AI services with invalid keys, request more questions than available with AI fallback disabled) and use a WebSocket test client to verify that the correct error events and payloads are emitted. Unit test the error mapping logic that translates internal exceptions to WebSocket error structures."}, {"id": 5, "title": "Implement Comprehensive Logging for All Error Handling Mechanisms", "description": "Implement detailed and structured logging using NestJS `LoggerService` across all newly implemented error handling components (MongoDB retries, insufficient content fallbacks, AI service chain, WebSocket error reporting) to facilitate robust debugging, monitoring, and operational auditing.", "dependencies": [1, 2, 3, 4], "details": "Ensure comprehensive logging is added for: \n1. MongoDB retries (Subtask 1): Log each retry attempt, delay duration, specific error triggering retry, and final outcome (success or failure after all retries).\n2. Insufficient questions & AI fallback (Subtask 2): Log when a deficit is detected, the number of questions requested vs. found, and when the AI fallback mechanism is activated or skipped due to configuration.\n3. AI service chain (Subtask 3): Log each attempt to an AI provider (OpenAI via OpenRouter, Google AI) or Cached Content, including parameters sent (if not sensitive), success/failure status, error messages from providers, and which provider ultimately succeeded or if all failed.\n4. WebSocket error communication (Subtask 4): Log the error event type and payload being sent to the client via WebSockets.\n5. General error conditions: Log any unexpected exceptions caught within the error handling logic itself, providing stack traces and context (e.g., worksheet ID, user ID if available). Use structured logging (JSON format if possible) for easier parsing and analysis by log management systems.\n<info added on 2025-06-10T09:53:28.471Z>\nAI service chain (Subtask 3): Log each attempt to an AI provider (OpenAI SDK via OpenRouter API, Google AI) or Cached Content, including parameters sent (if not sensitive), success/failure status, error messages from providers, and which provider ultimately succeeded or if all failed.\n</info added on 2025-06-10T09:53:28.471Z>", "status": "done", "testStrategy": "During the testing of subtasks 1-4, actively inspect application logs to verify that all specified events, retries, fallbacks, and errors are logged correctly, with sufficient detail and appropriate log levels (e.g., INFO, WARN, ERROR). Perform code reviews to ensure logging statements are present at all critical decision points and error paths. Simulate various failure scenarios and confirm logs provide a clear audit trail."}]}, {"id": 22, "title": "Implement Monitoring and Analytics System for Question Pool Selection", "description": "Develop a comprehensive monitoring and analytics system to track key performance, quality, and utilization metrics for the random question pool selection process. This includes an admin dashboard for visualizing these metrics.", "details": "1. **Metric Collection Service:** Create a `MonitoringModule` and `PoolMonitoringService`. Integrate with `QuestionPoolService` (Task 16), `WorksheetGenerateConsumer` (Task 17), `WorksheetDocumentCacheService` (Task 19), and `ContentValidationService` (Task 9) to capture relevant events and data. Implement mechanisms to log/store metrics, potentially in MongoDB collections or a time-series database.\n2. **Key Metrics Implementation:** Track and calculate: \n    a. **Pool Utilization Rate:** Percentage of unique questions used from the pool over time.\n    b. **Question Reuse Frequency:** How often individual questions are selected.\n    c. **Generation Time Comparison:** Average time for question selection from pool vs. AI generation (from Task 17).\n    d. **Validation Success Rate:** Success/failure rate of content validation (from Task 9).\n    e. **Distribution Adherence:** How well selected questions meet specified cognitive level/type distributions (from Task 18).\n    f. **Query Response Times:** Average/percentile response times for `QuestionPoolService.getRandomQuestions` (from Task 16/19).\n    g. **Cache Hit/Miss Ratios:** Track hit/miss ratios for the question pool cache (from Task 19).\n3. **Admin Dashboard Development:** Create a new secured section in the admin interface (requires Task 3 for authentication/authorization). Display collected metrics using charts and tables. Dashboard components should include an overview of key metrics, time-series charts for utilization, reuse, generation times, response times, validation success rates, distribution adherence visualization, and cache performance statistics.\n4. **Data Storage for Metrics:** Define schemas for storing aggregated metrics. Implement data aggregation jobs (e.g., hourly, daily) if raw event logging is used.", "testStrategy": "1. **Unit Tests:** Verify `PoolMonitoringService` for correct metric calculation logic using mocked dependent services.\n2. **Integration Tests:** Ensure events from `QuestionPoolService`, `WorksheetGenerateConsumer`, etc., are correctly captured and stored by the monitoring service. Test data aggregation if implemented.\n3. **End-to-End Tests (Admin Dashboard):** \n    a. Log in as an admin user (Task 3).\n    b. Navigate to the monitoring dashboard.\n    c. Trigger actions that generate metrics (e.g., request worksheets).\n    d. Verify the dashboard displays updated and accurate metrics for: pool utilization, question reuse, generation times (pool vs. AI), validation success/failure rates, distribution adherence, query response times, and cache hit/miss ratios.\n4. **Performance Tests:** Ensure metric collection does not significantly degrade the performance of core question selection services.", "status": "pending", "dependencies": [3, 9, 16, 17, 18, 19], "priority": "medium", "subtasks": [{"id": 1, "title": "Initialize Monitoring Module and PoolMonitoringService for Event Capture", "description": "Create the `MonitoringModule` and `PoolMonitoringService`. Implement basic event listeners or hooks within `QuestionPoolService` (Task 16), `WorksheetGenerateConsumer` (Task 17), `WorksheetDocumentCacheService` (Task 19), and `ContentValidationService` (Task 9) to capture raw data points relevant for future metric calculation. Focus on capturing events like question selection, generation attempts, validation results, and cache interactions.", "dependencies": [], "details": "Define interfaces for event data (e.g., `QuestionSelectedEvent`, `ValidationAttemptEvent`). Implement initial logging of these raw events, potentially to a structured log stream or a temporary staging area in a database. This subtask focuses on *capturing* data, not comprehensive processing or permanent storage of aggregated metrics yet. Ensure `PoolMonitoringService` has methods to receive these events from the respective services.", "status": "pending", "testStrategy": "Unit test the `PoolMonitoringService` for correct event reception and basic logging. Perform integration tests with `QuestionPoolService` and `ContentValidationService` to ensure events are triggered and captured by the `PoolMonitoringService`."}, {"id": 2, "title": "Design Metric Schemas and Implement Data Storage and Aggregation", "description": "Define the database schemas (e.g., MongoDB collections or time-series database structures) for storing both raw event data (if needed for detailed analysis) and aggregated metrics. Implement initial data aggregation jobs (e.g., hourly/daily roll-ups) for key metrics if raw event logging is the primary capture method. Choose an appropriate data store (e.g., MongoDB, Prometheus/InfluxDB).", "dependencies": [1], "details": "For MongoDB, define collections like `raw_monitoring_events`, `hourly_pool_utilization`, `daily_question_reuse_frequency`, etc. Design schemas considering query patterns for the dashboard. Implement aggregation pipelines or scheduled jobs (e.g., using cron and scripts, or a framework like NestJS Scheduler) to process raw events from Subtask 1 and populate aggregated metric tables/collections.", "status": "pending", "testStrategy": "Validate schema creation in the chosen database. Test data insertion for raw events and aggregated metrics. Write unit tests for aggregation logic using sample raw data to ensure correct calculation and storage of aggregated metrics."}, {"id": 3, "title": "Implement Core Metric Calculation Logic: Utilization, Reuse, and Generation Time", "description": "Develop the logic within `PoolMonitoringService` or a dedicated analytics component to calculate: Pool Utilization Rate, Question Reuse Frequency, and Generation Time Comparison (Pool vs. AI). This involves processing the captured events/data (from Subtask 1, stored/aggregated via Subtask 2) and ensuring the calculated metrics are stored according to the defined schemas.", "dependencies": [2], "details": "Pool Utilization: (Unique questions used from pool / Total unique questions in active pools) over time. Question Reuse: Frequency count for each question ID. Generation Time Comparison: Average time for `QuestionPoolService.getRandomQuestions` (Task 16/19) vs. average time for AI generation (Task 17). Ensure these calculations are integrated into the aggregation jobs or performed by the service, with results stored in the metrics database.", "status": "pending", "testStrategy": "Unit test the calculation logic for each metric with mock event data and expected outputs. Verify that the calculated metrics are correctly stored in the database with appropriate timestamps and dimensions."}, {"id": 4, "title": "Implement Supporting Metric Calculation Logic: Validation, Distribution, Query Times, Cache", "description": "Extend the monitoring system to calculate: Validation Success Rate (from `ContentValidationService` events - Task 9), Distribution Adherence (comparing selected questions' metadata against specified cognitive level/type distributions from Task 18), Query Response Times for `QuestionPoolService.getRandomQuestions` (Task 16/19), and Cache Hit/Miss Ratios for `WorksheetDocumentCacheService` (Task 19).", "dependencies": [3], "details": "Validation Success Rate: (Successful validations / Total validations). Distribution Adherence: Analyze batches of selected questions. Query Response Times: Log and aggregate (avg, p95, p99) response times. Cache Hit/Miss: (Cache hits / Total cache lookups). Integrate these calculations into the existing aggregation/processing flow and store results in the metrics database.", "status": "pending", "testStrategy": "Unit test calculation logic for each new metric. Conduct integration tests to ensure data flows correctly from source services (`ContentValidationService`, `WorksheetDocumentCacheService`, `QuestionPoolService`) through event capture to metric calculation and storage."}, {"id": 5, "title": "Develop Admin Dashboard for Visualizing Question Pool Metrics", "description": "Create a new secured section in the admin interface (leveraging Task 3 for authentication/authorization) to display all collected and calculated metrics. Implement UI components including an overview of key metrics, time-series charts for utilization, reuse, generation times, response times, validation success rates, distribution adherence visualization, and cache performance statistics.", "dependencies": [4], "details": "Design API endpoints to serve aggregated metrics data to the frontend. Use a charting library (e.g., Chart.js, Recharts, Nivo) for visualizations. Dashboard should allow filtering by date ranges. Ensure the UI is intuitive and provides actionable insights. Implement role-based access control for this new admin section.", "status": "pending", "testStrategy": "Perform UI/UX testing for dashboard components, ensuring data is displayed accurately and charts are rendered correctly. Test API endpoints for performance and correctness. Verify security by testing access with different user roles."}]}, {"id": 23, "title": "Implement Comprehensive Testing Suite for Random Question Pool Selection Feature", "description": "Develop a comprehensive testing suite covering unit, integration, performance, end-to-end, and validation tests for the random question pool selection feature and its integration into worksheet generation.", "details": "Implement a robust testing suite for the random question pool selection functionality. \n1. **Testing Frameworks**: Utilize <PERSON><PERSON> for unit and integration tests. Employ Playwright or <PERSON><PERSON> for end-to-end tests. For performance testing, use tools like k6 or Artillery.\n2. **Unit Tests**:\n    - `QuestionPoolService.getRandomQuestions()`: Mock MongoDB interactions. Test various filter combinations (subject, grade, type, language, cognitiveLevel), count parameter, edge cases (empty pool, insufficient questions matching criteria), and correct application of distribution rules by verifying inputs to the aggregation pipeline.\n    - Distribution Algorithm Unit Tests: Isolate and test algorithms for weighted selection, diversity mechanisms, cognitive level balancing, and question type balancing as implemented in Task 18.\n3. **Integration Tests**:\n    - `WorksheetGenerateConsumer` with `QuestionPoolService`: Verify correct invocation of `getRandomQuestions` with appropriate `WorksheetGenerationOptions`. Test handling of successful responses, errors, and fallback mechanisms (e.g., AI fallback if pool selection fails, as per Task 17).\n    - `QuestionPoolService` with `WorksheetDocumentCacheService` (Task 19): Test caching logic, including cache hits, misses, and impact on response times.\n    - Interaction with Configuration Management (Task 20): Ensure tests can run with different configurations (e.g., feature flags for selection strategies, different distribution rule settings).\n4. **Performance Tests**:\n    - `QuestionPoolService.getRandomQuestions()` MongoDB Aggregation (Task 16): Measure query execution time and system load under various data volumes and concurrent request scenarios. Identify and report bottlenecks.\n    - Caching Impact (Task 19): Quantify performance improvements due to caching strategies.\n5. **End-to-End (E2E) Tests**:\n    - Simulate complete user workflows for worksheet generation using the hybrid approach (pool selection primary, AI fallback as per Task 17).\n    - Verify the entire flow: API request -> `WorksheetGenerateConsumer` -> `QuestionPoolService` -> (optional AI sourcing via Task 12 if part of hybrid model) -> Worksheet result.\n    - Include scenarios requiring user authentication (Task 3).\n    - If applicable to the workflow, test scenarios involving WebSocket communication for progress updates or result delivery (Task 14), especially for error handling aspects covered in Task 21.\n6. **Validation Tests**:\n    - Educational Content Quality: Based on rules from Task 9, implement automated checks on questions selected from the pool for basic quality markers (e.g., format validity, completeness of required fields).\n    - Cognitive Level Distribution: Verify that batches of selected questions adhere to the specified cognitive level distributions defined in Task 18.\n    - Question Type Distribution: Confirm that selected questions meet the question type balancing rules from Task 18.\n7. **Error Handling Tests** (covering Task 21):\n    - Test graceful degradation and fallback strategies when the pool has insufficient content.\n    - Simulate and verify system behavior during database connectivity issues for `QuestionPoolService`.\n    - Test AI service failure fallbacks if E2E tests cover this part of the hybrid model from Task 17.\n    - Test error handling related to WebSocket communication if part of the E2E workflow or covered by Task 21.", "testStrategy": "1. **Code Review**: All test code (unit, integration, E2E, performance, validation) will be peer-reviewed for correctness, clarity, and comprehensive coverage.\n2. **Test Execution & Passing**: All implemented tests must pass successfully in a dedicated test environment that mirrors production as closely as possible.\n3. **Coverage Reports**: Achieve a minimum of 85% statement and branch coverage for key modules, including `QuestionPoolService`, distribution algorithms, and relevant integration points in `WorksheetGenerateConsumer`, as reported by Jest's coverage tools.\n4. **Performance Test Analysis**: Performance test results, including response times and resource utilization under load, will be documented, analyzed, and compared against predefined performance benchmarks or baselines. Any identified bottlenecks will be reported.\n5. **E2E Test Validation**: E2E test scenarios must successfully simulate and validate critical user workflows for worksheet generation involving random question pool selection and the hybrid sourcing model. Video recordings or detailed logs of E2E test runs should be available for failures.\n6. **Validation Test Verification**: Demonstrate that the implemented validation tests correctly identify adherence to, and violations of, educational content quality standards and distribution rules for cognitive levels and question types.\n7. **CI/CD Integration**: The entire testing suite must be integrated into the CI/CD pipeline, triggering automatically on relevant code changes. The pipeline must pass with all new tests before the task is considered complete.", "status": "pending", "dependencies": [3, 9, 12, 14, 16, 17, 18, 19, 20, 21], "priority": "medium", "subtasks": [{"id": 1, "title": "Implement Unit Tests for QuestionPoolService and Distribution Algorithms", "description": "Develop Jest unit tests for the `QuestionPoolService.getRandomQuestions()` method and the underlying distribution algorithms. This includes mocking dependencies like MongoDB, testing various filter combinations, edge cases, and verifying the correct application of distribution rules by inspecting inputs to the aggregation pipeline.", "dependencies": [], "details": "Use Jest as the testing framework.\nFor `QuestionPoolService.getRandomQuestions()`:\n- Mock MongoDB interactions (e.g., using `jest-mock-extended` or manual mocks).\n- Test with various filter combinations: subject, grade, type, language, cognitiveLevel.\n- Test the `count` parameter.\n- Test edge cases: empty question pool, insufficient questions matching criteria.\n- Verify inputs to the MongoDB aggregation pipeline to ensure distribution rules (Task 18) are correctly translated.\nFor Distribution Algorithm Unit Tests (from Task 18):\n- Isolate and test algorithms for weighted selection.\n- Test diversity mechanisms.\n- Test cognitive level balancing logic.\n- Test question type balancing logic.", "status": "pending", "testStrategy": "Run Jest tests in CI. Aim for high code coverage for `QuestionPoolService` and related algorithm modules. Focus on isolated logic verification."}, {"id": 2, "title": "Develop Integration Tests for Service Interactions and Configuration", "description": "Create Jest integration tests to verify the interactions between `QuestionPoolService` and other services like `WorksheetGenerateConsumer`, `WorksheetDocumentCacheService` (Task 19), and ensure correct behavior with Configuration Management (Task 20).", "dependencies": [1], "details": "Use Jest as the testing framework.\n`WorksheetGenerateConsumer` with `QuestionPoolService`:\n- Set up test instances of both services.\n- Verify correct invocation of `getRandomQuestions` with appropriate `WorksheetGenerationOptions`.\n- Test handling of successful responses and error propagation from `QuestionPoolService`.\n- Test fallback mechanisms (e.g., AI fallback as per Task 17 if pool selection fails).\n`QuestionPoolService` with `WorksheetDocumentCacheService` (Task 19):\n- Test caching logic: cache hits (data served from cache), cache misses (data fetched from DB and cached).\n- Verify cache key generation and data integrity from cache.\nInteraction with Configuration Management (Task 20):\n- Ensure tests can run with different configurations loaded (e.g., feature flags for selection strategies, different distribution rule settings).\n- Test that `QuestionPoolService` behavior changes correctly based on active configuration.", "status": "pending", "testStrategy": "Run Jest integration tests in CI. Focus on contract testing between services and correct data flow. Use in-memory versions of external dependencies where feasible."}, {"id": 3, "title": "Implement Performance Tests for Question Pool Selection and Caching", "description": "Develop performance tests using k6 or Artillery to measure the performance of `QuestionPoolService.getRandomQuestions()` MongoDB aggregation (Task 16) and quantify the impact of caching strategies (Task 19).", "dependencies": [2], "details": "Choose a performance testing tool (k6 or Artillery).\n`QuestionPoolService.getRandomQuestions()` MongoDB Aggregation (Task 16):\n- Design scenarios with varying data volumes in the MongoDB `questions` collection (e.g., 10k, 100k, 1M questions).\n- Simulate different filter complexities and `count` parameters.\n- Measure query execution time, p95/p99 response latency, and system load (CPU, memory) under various concurrent request scenarios (e.g., 10, 50, 100 RPS).\n- Identify and report potential bottlenecks in the aggregation pipeline or service logic.\nCaching Impact (Task 19):\n- Run tests with caching enabled and disabled against `QuestionPoolService` endpoints.\n- Measure and compare response times and throughput for cache hits vs. cache misses under load.\n- Quantify performance improvements (e.g., average response time reduction, throughput increase) due to caching.", "status": "pending", "testStrategy": "Run performance tests in a dedicated, production-like environment. Establish baseline performance metrics and integrate into CI to track regressions over time. Define clear SLOs for response times."}, {"id": 4, "title": "Create End-to-End (E2E) Tests for Worksheet Generation Workflow", "description": "Implement E2E tests using <PERSON><PERSON> or Cypress to simulate the complete user workflow for worksheet generation. This includes the hybrid approach (pool selection primary, AI fallback as per Task 17), user authentication (Task 3), and WebSocket communication (Task 14) if applicable.", "dependencies": [3], "details": "Choose an E2E testing framework (Playwright or Cypress).\nSimulate complete user workflows for worksheet generation:\n- API request to trigger worksheet generation.\n- Verify correct processing by `WorksheetGenerateConsumer` and invocation of `QuestionPoolService`.\n- Test the hybrid model: successful pool selection, and fallback to AI sourcing (Task 12) if pool selection fails or criteria are not met.\n- Validate the structure and basic content of the final worksheet result.\nInclude scenarios requiring user authentication (Task 3) to ensure secure access to generation endpoints.\nIf applicable (Task 14, Task 21), test scenarios involving WebSocket communication for progress updates or result delivery, including connection and basic message validation.", "status": "pending", "testStrategy": "Run E2E tests in a staging or pre-production environment that mirrors production setup. Focus on critical user paths and happy path scenarios, plus key failure/fallback scenarios in the hybrid model."}, {"id": 5, "title": "Implement Validation and Granular Error Handling Tests", "description": "Develop tests to validate the educational content quality (Task 9) and distribution rules (Task 18) of selected questions. Also, implement specific tests for various error handling scenarios (Task 21) within the question pool selection feature.", "dependencies": [4], "details": "Validation Tests (primarily using Jest or helper scripts):\n- Educational Content Quality (Task 9): Implement automated checks on questions selected from the pool for format validity (e.g., presence of required fields like question text, options, answer) and basic quality markers.\n- Cognitive Level Distribution (Task 18): After `getRandomQuestions` selects a batch, programmatically verify that the selected questions adhere to the specified cognitive level distributions.\n- Question Type Distribution (Task 18): Confirm that selected questions meet the question type balancing rules.\nSpecific Error Handling Tests (Task 21, using Jest for service-level or Playwright/Cypress for E2E if needed):\n- Test graceful degradation and fallback strategies when the question pool has insufficient content for given criteria (verify appropriate error messages or fallback behavior).\n- Simulate database connectivity issues for `QuestionPoolService` (e.g., by mocking DB connection errors at the service layer) and verify system behavior (e.g., retries, error responses, AI fallback triggering).\n- Test AI service failure fallbacks (Task 17) specifically if the AI service becomes unavailable or returns errors during the fallback process.\n- Test error handling related to WebSocket communication (Task 14, Task 21) for scenarios like connection drops or error messages during worksheet generation progress/result delivery.", "status": "pending", "testStrategy": "Combine automated Jest tests for validation logic against test data or service responses. For error handling, use a mix of unit/integration tests with mocks for specific failure conditions and targeted E2E scenarios to verify user-facing error messages or recovery."}]}]}