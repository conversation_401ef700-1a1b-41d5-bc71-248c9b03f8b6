import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { QuestionPool } from '../mongodb/schemas/question-pool.schema';
import { ExerciseQuestionItem } from '../prompt/interfaces/exercise-result.interface';
import {
  QuestionSelectionParams,
  QuestionSelectionResult,
  QuestionDistributionConfig,
  DifficultyDistribution,
  QuestionTypeBalancing,
  DiversityConfig,
  QualityValidationConfig,
  WeightedQuestion
} from './interfaces/distribution.interface';
import { ContentValidationService } from '../validation/content-validation.service';
import { QuestionPoolConfigService } from './question-pool-config.service';
import { QuestionPoolCacheService } from './services/question-pool-cache.service';
import { QuestionPoolMetricsService } from './services/question-pool-metrics.service';
import { withRetry, RetryOptions } from '../../core/utils/retry.util';
import { WebSocketErrorCode } from '../../core/enums/websocket-error-codes.enum';

@Injectable()
export class QuestionPoolService {
  private readonly logger = new Logger(QuestionPoolService.name);

  constructor(
    @InjectModel(QuestionPool.name)
    private questionPoolModel: Model<QuestionPool>,
    private contentValidationService: ContentValidationService,
    private configService: QuestionPoolConfigService,
    private cacheService: QuestionPoolCacheService,
    private metricsService: QuestionPoolMetricsService,
  ) {}

  /**
   * Execute MongoDB operations with retry logic for transient failures
   * @param operation The MongoDB operation to execute
   * @param operationName Name of the operation for logging
   * @returns The result of the operation
   */
  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    operationName: string
  ): Promise<T> {
    const retryOptions: RetryOptions = {
      maxRetries: 3,
      initialDelay: 1000,
      maxDelay: 10000,
      backoffMultiplier: 2,
      jitter: true,
      retryCondition: (error: any) => {
        // Retry on transient MongoDB errors
        const retryableErrors = [
          'MongoNetworkError',
          'MongoTimeoutError',
          'MongoServerSelectionError',
          'MongoWriteConcernError',
          'ENOTFOUND',
          'ECONNREFUSED',
          'ETIMEDOUT',
          'ECONNRESET'
        ];

        const errorName = error.constructor.name;
        const errorCode = error.code;
        const errorMessage = error.message?.toLowerCase() || '';

        // Check for retryable error types
        if (retryableErrors.includes(errorName)) {
          this.logger.warn(`Retryable MongoDB error detected: ${errorName} - ${error.message}`);
          return true;
        }

        // Check for specific error codes
        if (errorCode && ['ENOTFOUND', 'ECONNREFUSED', 'ETIMEDOUT', 'ECONNRESET'].includes(errorCode)) {
          this.logger.warn(`Retryable network error detected: ${errorCode} - ${error.message}`);
          return true;
        }

        // Check for connection-related error messages
        if (errorMessage.includes('connection') ||
            errorMessage.includes('timeout') ||
            errorMessage.includes('network') ||
            errorMessage.includes('server selection')) {
          this.logger.warn(`Retryable connection error detected: ${error.message}`);
          return true;
        }

        // Don't retry on validation errors, duplicate key errors, etc.
        this.logger.debug(`Non-retryable error detected: ${errorName} - ${error.message}`);
        return false;
      },
      onRetry: (attempt: number, error: any, delay: number) => {
        this.logger.warn(
          `MongoDB operation '${operationName}' failed (attempt ${attempt}), retrying in ${delay}ms. Error: ${error.message}`
        );
      }
    };

    const result = await withRetry(operation, retryOptions, this.logger);

    if (!result.success) {
      this.logger.error(
        `MongoDB operation '${operationName}' failed after ${result.attempts} attempts: ${result.lastError?.message}`,
        result.lastError?.stack
      );
      throw result.lastError;
    }

    if (result.attempts > 1) {
      this.logger.log(
        `MongoDB operation '${operationName}' succeeded after ${result.attempts} attempts in ${result.totalTime}ms`
      );
    }

    return result.result!;
  }

  /**
   * Add a question to the pool
   * @param question The question to add
   * @param optionValue Optional reference to option values used
   * @returns The saved question
   */
  async addQuestion(
    question: ExerciseQuestionItem,
    optionValue?: Record<string, any>,
  ): Promise<QuestionPool> {
    try {
      const newQuestion = new this.questionPoolModel({
        type: question.type,
        content: question.content,
        options: question.options,
        answer: question.answer,
        explain: question.explain,
        imagePrompt: question.imagePrompt,
        image: question.image,
        subject: question.subject, // Parent subject
        parentSubject: question.parentSubject, // Subject type parent
        childSubject: question.childSubject, // Subject type child
        grade: question.grade, // Grade level
        language: question.language || 'English', // Language (default to English)
        optionValue: optionValue,
        status: 'active',
      });

      return await newQuestion.save();
    } catch (error) {
      this.logger.error(`Error adding question to pool: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Add multiple questions to the pool
   * @param questions The questions to add
   * @param optionValue Optional reference to option values used
   * @returns The saved questions
   */
  async addQuestions(
    questions: ExerciseQuestionItem[],
    optionValue?: Record<string, any>,
  ): Promise<QuestionPool[]> {
    try {
      const savedQuestions: QuestionPool[] = [];

      for (const question of questions) {
        const savedQuestion = await this.addQuestion(question, optionValue);
        savedQuestions.push(savedQuestion);
      }

      return savedQuestions;
    } catch (error) {
      this.logger.error(`Error adding questions to pool: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get questions from the pool with filtering
   * @param filters Filters to apply
   * @param limit Maximum number of questions to return
   * @param skip Number of questions to skip (for pagination)
   * @returns The filtered questions
   */
  async getQuestions(
    filters: {
      subject?: string;
      parentSubject?: string;
      childSubject?: string;
      type?: string;
      optionValueId?: string;
      status?: string;
      grade?: string;
      language?: string;
    },
    limit: number = 10,
    skip: number = 0,
  ): Promise<{ questions: QuestionPool[]; total: number }> {
    try {
      const query: any = { status: 'active' };

      if (filters.subject) {
        query.subject = filters.subject;
      }

      if (filters.parentSubject) {
        query.parentSubject = filters.parentSubject;
      }

      if (filters.childSubject) {
        query.childSubject = filters.childSubject;
      }

      if (filters.type) {
        query.type = filters.type;
      }

      if (filters.optionValueId) {
        query['optionValue.id'] = filters.optionValueId;
      }

      if (filters.status) {
        query.status = filters.status;
      }

      if (filters.grade) {
        query.grade = filters.grade;
      }

      if (filters.language) {
        query.language = filters.language;
      }

      const [questions, total] = await Promise.all([
        this.questionPoolModel
          .find(query)
          .sort({ createdAt: -1 })
          .limit(limit)
          .skip(skip)
          .exec(),
        this.questionPoolModel.countDocuments(query).exec(),
      ]);

      return { questions, total };
    } catch (error) {
      this.logger.error(`Error getting questions from pool: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update a question's status
   * @param id The question ID
   * @param status The new status
   * @returns The updated question
   */
  async updateQuestionStatus(
    id: string,
    status: 'active' | 'inactive',
  ): Promise<QuestionPool | null> {
    try {
      return await this.questionPoolModel.findByIdAndUpdate(
        id,
        { status },
        { new: true },
      );
    } catch (error) {
      this.logger.error(`Error updating question status: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get random questions from the pool with filtering (legacy method for backward compatibility)
   * @param filters Filters to apply
   * @param count Number of random questions to return
   * @returns The randomly selected questions
   */
  async getRandomQuestions(
    filters: {
      subject?: string;
      parentSubject?: string;
      childSubject?: string;
      type?: string | string[];
      difficulty?: string;
      status?: string;
      grade?: string;
      language?: string;
    },
    count: number = 10,
  ): Promise<QuestionPool[]> {
    // Convert legacy parameters to new format
    const params: QuestionSelectionParams = {
      subject: filters.subject,
      parentSubject: filters.parentSubject,
      childSubject: filters.childSubject,
      type: filters.type,
      status: filters.status,
      grade: filters.grade,
      language: filters.language,
      count,
      skipDistribution: true, // Skip distribution for legacy calls
      skipDiversity: true,
      skipValidation: true,
    };

    const result = await this.getRandomQuestionsWithDistribution(params);
    return result.questions;
  }

  /**
   * Get random questions with distribution enforcement and balancing
   * @param params Selection parameters including distribution configuration
   * @returns Selection result with metadata
   */
  async getRandomQuestionsWithDistribution(params: QuestionSelectionParams): Promise<QuestionSelectionResult> {
    const startTime = Date.now();
    const fallbacksTriggered: string[] = [];
    let cacheStatus: 'hit' | 'miss' | 'disabled' = 'disabled';

    try {
      // Record the query attempt
      this.metricsService.recordQuery('getRandomQuestionsWithDistribution', 'success');
      this.metricsService.updateQuestionCounts(params.count || 10, 0);

      // Try to get from cache first (only for non-diversity queries to avoid stale selection tracking)
      if (!params.skipDiversity) {
        const cachedResult = await this.cacheService.getFromCache(params);
        if (cachedResult) {
          cacheStatus = 'hit';
          this.metricsService.recordCacheHit();

          const duration = (Date.now() - startTime) / 1000;
          this.metricsService.recordQueryDuration(
            duration,
            'getRandomQuestionsWithDistribution',
            cacheStatus,
            !params.skipDistribution,
            !params.skipDiversity
          );
          this.metricsService.updateQuestionCounts(params.count || 10, cachedResult.questions.length);

          this.logger.debug(`Returning cached result with ${cachedResult.questions.length} questions`);
          return cachedResult;
        } else {
          cacheStatus = 'miss';
          this.metricsService.recordCacheMiss();
        }
      }

      // Get default configuration and merge with provided config
      const config = await this.getEffectiveDistributionConfig(params.distributionConfig);

      // Build base query
      const baseQuery = this.buildBaseQuery(params);

      let result: QuestionSelectionResult;

      // If distribution is skipped, use simple random selection
      if (params.skipDistribution) {
        result = await this.performSimpleRandomSelection(baseQuery, params, startTime);
      } else {
        // Perform distribution-based selection
        result = await this.performDistributionBasedSelection(baseQuery, params, config, fallbacksTriggered, startTime);
      }

      // Apply diversity algorithms if enabled and not skipped
      if (config.diversity.enabled && !params.skipDiversity) {
        result.questions = await this.applyDiversityAlgorithms(result.questions, config.diversity);
      }

      // Apply quality validation if enabled and not skipped
      if (config.qualityValidation.enabled && !params.skipValidation) {
        const validationResult = await this.applyQualityValidation(result.questions, config.qualityValidation, baseQuery, params.count);
        result.questions = validationResult.validatedQuestions;
        result.metadata.validationStats = validationResult.validationStats;

        if (validationResult.fallbacksTriggered.length > 0) {
          fallbacksTriggered.push(...validationResult.fallbacksTriggered);
        }
      }

      // Update selection tracking for selected questions
      if (result.questions.length > 0) {
        const questionIds = result.questions.map(q => q._id?.toString()).filter(Boolean);
        await this.updateQuestionSelectionTracking(questionIds);
      }

      // Update final metadata
      result.metadata.fallbacksTriggered = fallbacksTriggered;
      result.metadata.selectionTime = Date.now() - startTime;

      // Cache the result if it's successful and not using diversity (to avoid stale tracking)
      if (result.questions.length > 0 && params.skipDiversity) {
        await this.cacheService.saveToCache(params, result);
      }

      // Record metrics
      const duration = (Date.now() - startTime) / 1000;
      this.metricsService.recordQueryDuration(
        duration,
        'getRandomQuestionsWithDistribution',
        cacheStatus,
        !params.skipDistribution,
        !params.skipDiversity
      );
      this.metricsService.updateQuestionCounts(params.count || 10, result.questions.length);

      return result;

    } catch (error) {
      this.metricsService.recordError('getRandomQuestionsWithDistribution', error.constructor.name);
      this.metricsService.recordQuery('getRandomQuestionsWithDistribution', 'error');

      const duration = (Date.now() - startTime) / 1000;
      this.metricsService.recordQueryDuration(
        duration,
        'getRandomQuestionsWithDistribution',
        cacheStatus,
        !params.skipDistribution,
        !params.skipDiversity
      );

      this.logger.error(`Error in getRandomQuestionsWithDistribution: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get effective distribution configuration by merging defaults with provided config
   */
  private async getEffectiveDistributionConfig(providedConfig?: Partial<QuestionDistributionConfig>): Promise<QuestionDistributionConfig> {
    const defaultConfig = this.configService.getConfig().distribution;

    return {
      difficultyDistribution: {
        ...defaultConfig.defaultDifficultyDistribution,
        ...(providedConfig?.difficultyDistribution || {}),
      },
      questionTypeBalancing: {
        ...defaultConfig.defaultQuestionTypeBalancing,
        ...(providedConfig?.questionTypeBalancing || {}),
      },
      diversity: {
        ...defaultConfig.defaultDiversityConfig,
        ...(providedConfig?.diversity || {}),
      },
      qualityValidation: {
        ...defaultConfig.defaultQualityValidationConfig,
        ...(providedConfig?.qualityValidation || {}),
      },
      fallback: {
        ...defaultConfig.defaultFallbackConfig,
        ...(providedConfig?.fallback || {}),
      },
    };
  }

  /**
   * Build base MongoDB query from selection parameters
   */
  private buildBaseQuery(params: QuestionSelectionParams): any {
    const query: any = { status: params.status || 'active' };

    if (params.subject) {
      query.subject = params.subject;
    }

    if (params.parentSubject) {
      query.parentSubject = params.parentSubject;
    }

    if (params.childSubject) {
      query.childSubject = params.childSubject;
    }

    if (params.type) {
      if (Array.isArray(params.type)) {
        query.type = { $in: params.type };
      } else {
        query.type = params.type;
      }
    }

    if (params.grade) {
      query.grade = params.grade;
    }

    if (params.language) {
      query.language = params.language;
    }

    return query;
  }

  /**
   * Perform simple random selection without distribution enforcement
   */
  private async performSimpleRandomSelection(
    baseQuery: any,
    params: QuestionSelectionParams,
    startTime: number
  ): Promise<QuestionSelectionResult> {
    const dbQueryStart = Date.now();

    const randomQuestions = await this.executeWithRetry(
      () => this.questionPoolModel.aggregate([
        { $match: baseQuery },
        { $sample: { size: params.count } }
      ]).exec(),
      'simple_random_selection'
    );

    const dbQueryDuration = (Date.now() - dbQueryStart) / 1000;
    this.metricsService.recordDbQueryDuration(dbQueryDuration, 'simple_random_selection');

    const questions = randomQuestions;

    return {
      questions,
      metadata: {
        totalRequested: params.count,
        totalReturned: questions.length,
        distributionAchieved: {},
        fallbacksTriggered: [],
        validationStats: {
          totalValidated: 0,
          passed: 0,
          failed: 0,
          successRate: 1,
        },
        selectionTime: Date.now() - startTime,
      },
    };
  }

  /**
   * Perform distribution-based selection with difficulty level enforcement
   */
  private async performDistributionBasedSelection(
    baseQuery: any,
    params: QuestionSelectionParams,
    config: QuestionDistributionConfig,
    fallbacksTriggered: string[],
    startTime: number
  ): Promise<QuestionSelectionResult> {
    // Calculate target counts for each difficulty level
    const targetCounts = this.calculateDifficultyTargetCounts(params.count, config.difficultyDistribution);

    // Select questions by difficulty level with type balancing
    const selectedQuestions: QuestionPool[] = [];
    const actualDistribution: Record<string, number> = {};

    for (const [difficulty, targetCount] of Object.entries(targetCounts)) {
      if (targetCount > 0) {
        let questions: any[];

        if (config.questionTypeBalancing.enabled) {
          // Use type-balanced selection for this difficulty level
          questions = await this.selectQuestionsWithTypeBalancing(
            baseQuery,
            difficulty,
            targetCount,
            config.questionTypeBalancing
          );
        } else {
          // Simple random selection for this difficulty level
          const difficultyQuery = { ...baseQuery, difficultyLevel: difficulty };
          questions = await this.executeWithRetry(
            () => this.questionPoolModel.aggregate([
              { $match: difficultyQuery },
              { $sample: { size: targetCount } }
            ]).exec(),
            `difficulty_selection_${difficulty}`
          );
        }

        selectedQuestions.push(...questions);
        actualDistribution[difficulty] = questions.length;

        // Log if we couldn't get enough questions for this difficulty
        if (questions.length < targetCount) {
          const shortfall = targetCount - questions.length;
          this.logger.warn(`Could not find enough ${difficulty} questions. Requested: ${targetCount}, Found: ${questions.length}, Shortfall: ${shortfall}`);

          if (config.fallback.logFallbackReasons) {
            fallbacksTriggered.push(`Insufficient ${difficulty} questions: ${shortfall} short`);
          }
        }
      }
    }

    // Handle shortfall if we don't have enough questions
    if (selectedQuestions.length < params.count && config.fallback.allowBestEffort) {
      const shortfall = params.count - selectedQuestions.length;

      if (config.fallback.relaxDistributionOnShortfall) {
        // Try to fill the gap with any available questions
        const additionalQuery = {
          ...baseQuery,
          _id: { $nin: selectedQuestions.map(q => q._id) }
        };

        const additionalQuestions = await this.executeWithRetry(
          () => this.questionPoolModel.aggregate([
            { $match: additionalQuery },
            { $sample: { size: shortfall } }
          ]).exec(),
          'additional_questions_fallback'
        );

        if (additionalQuestions && Array.isArray(additionalQuestions)) {
          selectedQuestions.push(...additionalQuestions);

          if (config.fallback.logFallbackReasons) {
            fallbacksTriggered.push(`Added ${additionalQuestions.length} additional questions to meet count`);
          }
        }
      }
    }

    // Calculate actual distribution percentages
    const distributionAchieved: Record<string, number> = {};
    if (selectedQuestions.length > 0) {
      for (const [difficulty, count] of Object.entries(actualDistribution)) {
        distributionAchieved[difficulty] = count / selectedQuestions.length;
      }
    }

    return {
      questions: selectedQuestions,
      metadata: {
        totalRequested: params.count,
        totalReturned: selectedQuestions.length,
        distributionAchieved,
        fallbacksTriggered,
        validationStats: {
          totalValidated: 0,
          passed: 0,
          failed: 0,
          successRate: 1,
        },
        selectionTime: Date.now() - startTime,
      },
    };
  }

  /**
   * Calculate target counts for each difficulty level
   */
  private calculateDifficultyTargetCounts(totalCount: number, distribution: DifficultyDistribution): Record<string, number> {
    return {
      Easy: Math.round(totalCount * distribution.Easy),
      Medium: Math.round(totalCount * distribution.Medium),
      Advanced: Math.round(totalCount * distribution.Advanced),
    };
  }

  /**
   * Select questions with type balancing for a specific difficulty level
   */
  private async selectQuestionsWithTypeBalancing(
    baseQuery: any,
    difficulty: string,
    targetCount: number,
    typeBalancingConfig: QuestionTypeBalancing
  ): Promise<any[]> {
    const difficultyQuery = { ...baseQuery, difficultyLevel: difficulty };

    if (typeBalancingConfig.targetDistribution) {
      // Use specific target distribution for question types
      return await this.selectQuestionsWithSpecificTypeDistribution(
        difficultyQuery,
        targetCount,
        typeBalancingConfig.targetDistribution
      );
    } else if (typeBalancingConfig.preferDiversity) {
      // Use diversity-based selection to balance question types
      return await this.selectQuestionsWithTypeDiversity(difficultyQuery, targetCount);
    } else {
      // Fallback to simple random selection
      const questions = await this.questionPoolModel.aggregate([
        { $match: difficultyQuery },
        { $sample: { size: targetCount } }
      ]).exec();
      return questions;
    }
  }

  /**
   * Select questions with specific type distribution
   */
  private async selectQuestionsWithSpecificTypeDistribution(
    query: any,
    targetCount: number,
    typeDistribution: Record<string, number>
  ): Promise<any[]> {
    const selectedQuestions: any[] = [];

    for (const [questionType, percentage] of Object.entries(typeDistribution)) {
      const typeTargetCount = Math.round(targetCount * percentage);

      if (typeTargetCount > 0) {
        const typeQuery = { ...query, type: questionType };

        const typeQuestions = await this.questionPoolModel.aggregate([
          { $match: typeQuery },
          { $sample: { size: typeTargetCount } }
        ]).exec();

        if (typeQuestions && Array.isArray(typeQuestions)) {
          selectedQuestions.push(...typeQuestions);
        }
      }
    }

    // If we don't have enough questions, fill the gap with any available questions
    if (selectedQuestions.length < targetCount) {
      const shortfall = targetCount - selectedQuestions.length;
      const usedIds = selectedQuestions.map(q => q._id);

      const additionalQuery = {
        ...query,
        _id: { $nin: usedIds }
      };

      const additionalQuestions = await this.questionPoolModel.aggregate([
        { $match: additionalQuery },
        { $sample: { size: shortfall } }
      ]).exec();

      if (additionalQuestions && Array.isArray(additionalQuestions)) {
        selectedQuestions.push(...additionalQuestions);
      }
    }

    return selectedQuestions.slice(0, targetCount);
  }

  /**
   * Select questions with type diversity (balanced mix of available types)
   */
  private async selectQuestionsWithTypeDiversity(query: any, targetCount: number): Promise<any[]> {
    // First, get all available question types for this query
    const availableTypes = await this.questionPoolModel.distinct('type', query);

    if (availableTypes.length === 0) {
      return [];
    }

    const selectedQuestions: any[] = [];
    const questionsPerType = Math.floor(targetCount / availableTypes.length);
    const remainder = targetCount % availableTypes.length;

    // Select questions evenly across types
    for (let i = 0; i < availableTypes.length; i++) {
      const questionType = availableTypes[i];
      const typeTargetCount = questionsPerType + (i < remainder ? 1 : 0);

      if (typeTargetCount > 0) {
        const typeQuery = { ...query, type: questionType };

        const typeQuestions = await this.questionPoolModel.aggregate([
          { $match: typeQuery },
          { $sample: { size: typeTargetCount } }
        ]).exec();

        if (typeQuestions && Array.isArray(typeQuestions)) {
          selectedQuestions.push(...typeQuestions);
        }
      }
    }

    return selectedQuestions.slice(0, targetCount);
  }

  /**
   * Apply diversity algorithms to reduce repetition of recently or frequently selected questions
   */
  private async applyDiversityAlgorithms(questions: any[], diversityConfig: DiversityConfig): Promise<any[]> {
    if (!diversityConfig.enabled) {
      return questions;
    }

    const now = new Date();
    const recencyThresholdMs = diversityConfig.recencyThresholdHours * 60 * 60 * 1000;

    // Calculate diversity scores for each question
    const questionsWithScores = questions.map(question => {
      let diversityScore = 1.0; // Start with perfect score

      // Apply recency penalty
      if (question.lastSelectedTimestamp && diversityConfig.recencyPenaltyWeight > 0) {
        const timeSinceLastSelection = now.getTime() - new Date(question.lastSelectedTimestamp).getTime();
        if (timeSinceLastSelection < recencyThresholdMs) {
          const recencyPenalty = (1 - (timeSinceLastSelection / recencyThresholdMs)) * diversityConfig.recencyPenaltyWeight;
          diversityScore -= recencyPenalty;
        }
      }

      // Apply frequency penalty
      if (question.selectionFrequency && diversityConfig.frequencyPenaltyWeight > 0) {
        const frequency = question.selectionFrequency || 0;
        // Normalize frequency penalty (assuming max reasonable frequency is 10)
        const normalizedFrequency = Math.min(frequency / 10, 1);
        const frequencyPenalty = normalizedFrequency * diversityConfig.frequencyPenaltyWeight;
        diversityScore -= frequencyPenalty;
      }

      // Ensure score doesn't go below 0
      diversityScore = Math.max(0, diversityScore);

      return {
        question,
        diversityScore,
        reasons: [
          ...(question.lastSelectedTimestamp ? [`Last selected: ${question.lastSelectedTimestamp}`] : []),
          ...(question.selectionFrequency ? [`Selection frequency: ${question.selectionFrequency}`] : []),
        ],
      };
    });

    // Sort by diversity score (higher scores first) and return the questions
    questionsWithScores.sort((a, b) => b.diversityScore - a.diversityScore);

    // Log diversity information if needed
    if (questionsWithScores.some(q => q.diversityScore < 1.0)) {
      this.logger.debug('Applied diversity algorithms:', {
        questionsAffected: questionsWithScores.filter(q => q.diversityScore < 1.0).length,
        averageScore: questionsWithScores.reduce((sum, q) => sum + q.diversityScore, 0) / questionsWithScores.length,
      });
    }

    return questionsWithScores.map(q => q.question);
  }

  /**
   * Apply quality validation to selected questions
   */
  private async applyQualityValidation(
    questions: any[],
    validationConfig: QualityValidationConfig,
    baseQuery: any,
    originalCount: number
  ): Promise<{
    validatedQuestions: any[];
    validationStats: any;
    fallbacksTriggered: string[];
  }> {
    const fallbacksTriggered: string[] = [];

    // Convert questions to validation format
    const questionsForValidation = questions.map(q => ({
      id: q._id?.toString(),
      type: q.type,
      content: q.content,
      options: q.options,
      answer: q.answer,
      explain: q.explain,
      subject: q.subject,
      grade: q.grade,
      difficultyLevel: q.difficultyLevel,
    }));

    // Perform validation
    const validationResult = await this.contentValidationService.validateQuestions(questionsForValidation);

    // Filter questions based on validation results
    const validQuestions: any[] = [];
    const invalidQuestions: any[] = [];

    for (let i = 0; i < questions.length; i++) {
      const question = questions[i];
      const validation = validationResult.results[i];

      if (validation && validation.isValid) {
        validQuestions.push(question);
      } else {
        invalidQuestions.push({ question, validation });
      }
    }

    // Handle validation failures based on strategy
    let finalQuestions = validQuestions;

    if (invalidQuestions.length > 0) {
      this.logger.warn(`${invalidQuestions.length} questions failed validation`);

      switch (validationConfig.failureHandlingStrategy) {
        case 'replace':
          finalQuestions = await this.handleValidationFailuresWithReplacement(
            validQuestions,
            invalidQuestions,
            baseQuery,
            originalCount,
            validationConfig,
            fallbacksTriggered
          );
          break;

        case 'discard':
          // Just use valid questions, don't try to replace
          if (validQuestions.length < originalCount) {
            fallbacksTriggered.push(`Discarded ${invalidQuestions.length} invalid questions, returning ${validQuestions.length} instead of ${originalCount}`);
          }
          break;

        case 'log_and_proceed':
          // Include all questions but log the issues
          finalQuestions = questions;
          fallbacksTriggered.push(`Proceeding with ${invalidQuestions.length} questions that failed validation`);
          break;
      }
    }

    // Check if validation success rate meets minimum requirement
    const successRate = validationResult.summary.successRate;
    if (successRate < validationConfig.minValidationSuccessRate) {
      fallbacksTriggered.push(`Validation success rate ${(successRate * 100).toFixed(1)}% below minimum ${(validationConfig.minValidationSuccessRate * 100).toFixed(1)}%`);
    }

    return {
      validatedQuestions: finalQuestions,
      validationStats: validationResult.summary,
      fallbacksTriggered,
    };
  }

  /**
   * Handle validation failures by attempting to replace invalid questions
   */
  private async handleValidationFailuresWithReplacement(
    validQuestions: any[],
    invalidQuestions: any[],
    baseQuery: any,
    originalCount: number,
    validationConfig: QualityValidationConfig,
    fallbacksTriggered: string[]
  ): Promise<any[]> {
    let finalQuestions = [...validQuestions];
    const neededReplacements = Math.min(invalidQuestions.length, originalCount - validQuestions.length);

    if (neededReplacements <= 0) {
      return finalQuestions;
    }

    // Get IDs of questions we already have (valid + invalid) to avoid duplicates
    const existingIds = [...validQuestions, ...invalidQuestions.map(iq => iq.question)]
      .map(q => q._id)
      .filter(Boolean);

    let replacementAttempts = 0;
    let replacementsFound = 0;

    while (replacementsFound < neededReplacements && replacementAttempts < validationConfig.maxReplacementAttempts) {
      replacementAttempts++;

      // Try to find replacement questions
      const replacementQuery = {
        ...baseQuery,
        _id: { $nin: existingIds }
      };

      const candidateReplacements = await this.questionPoolModel.aggregate([
        { $match: replacementQuery },
        { $sample: { size: neededReplacements - replacementsFound } }
      ]).exec();

      if (!candidateReplacements || candidateReplacements.length === 0) {
        break; // No more questions available
      }

      // Validate the replacement candidates
      const candidatesForValidation = candidateReplacements.map(q => ({
        id: q._id?.toString(),
        type: q.type,
        content: q.content,
        options: q.options,
        answer: q.answer,
        explain: q.explain,
        subject: q.subject,
        grade: q.grade,
        difficultyLevel: q.difficultyLevel,
      }));

      const replacementValidationResult = await this.contentValidationService.validateQuestions(candidatesForValidation);

      // Add valid replacements
      for (let i = 0; i < candidateReplacements.length; i++) {
        const candidate = candidateReplacements[i];
        const validation = replacementValidationResult.results[i];

        if (validation && validation.isValid) {
          finalQuestions.push(candidate);
          existingIds.push(candidate._id);
          replacementsFound++;

          if (replacementsFound >= neededReplacements) {
            break;
          }
        }
      }
    }

    // Log replacement results
    if (replacementsFound > 0) {
      fallbacksTriggered.push(`Replaced ${replacementsFound} invalid questions after ${replacementAttempts} attempts`);
    }

    if (replacementsFound < neededReplacements) {
      const shortfall = neededReplacements - replacementsFound;
      fallbacksTriggered.push(`Could not find ${shortfall} valid replacement questions after ${replacementAttempts} attempts`);
    }

    return finalQuestions;
  }

  /**
   * Update questions to add difficultyLevel field based on optionValue.difficulty
   * This is a migration method to handle existing questions
   */
  async migrateDifficultyLevels(): Promise<{ updated: number; errors: number }> {
    try {
      this.logger.log('Starting difficulty level migration...');

      // Find questions without difficultyLevel but with optionValue.difficulty
      const questionsToUpdate = await this.questionPoolModel.find({
        difficultyLevel: { $exists: false },
        'optionValue.difficulty': { $exists: true }
      }).exec();

      let updated = 0;
      let errors = 0;

      for (const question of questionsToUpdate) {
        try {
          const difficulty = question.optionValue?.difficulty;
          if (difficulty && ['Easy', 'Medium', 'Advanced', 'Hard'].includes(difficulty)) {
            // Normalize 'Hard' to 'Advanced'
            const normalizedDifficulty = difficulty === 'Hard' ? 'Advanced' : difficulty;

            await this.questionPoolModel.updateOne(
              { _id: question._id },
              { $set: { difficultyLevel: normalizedDifficulty } }
            );
            updated++;
          }
        } catch (error) {
          this.logger.error(`Error updating question ${question._id}: ${error.message}`);
          errors++;
        }
      }

      this.logger.log(`Migration completed. Updated: ${updated}, Errors: ${errors}`);
      return { updated, errors };
    } catch (error) {
      this.logger.error(`Error during difficulty level migration: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update question selection tracking (for diversity algorithms)
   */
  async updateQuestionSelectionTracking(questionIds: string[]): Promise<void> {
    try {
      const now = new Date();

      await this.questionPoolModel.updateMany(
        { _id: { $in: questionIds } },
        {
          $set: { lastSelectedTimestamp: now },
          $inc: { selectionFrequency: 1 }
        }
      );
    } catch (error) {
      this.logger.error(`Error updating selection tracking: ${error.message}`, error.stack);
    }
  }
}
